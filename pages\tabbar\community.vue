<template>
  <view class="community-container">
    <c-loading3></c-loading3>
    <view class="self">
      <section class="nav-bar">
        <c-navBar title="社区" isPerch :isBack="false" titleAlign="left" :titleStyle="{
          'font-family': 'Bold, Source Han Serif SC',
          'font-weight': 'bold',
          'font-size': '44rpx'
        }"> </c-navBar>
      </section>
      <!-- 动态列表 -->
      <scroll-view scroll-y class="main-scroll" @scrolltolower="loadMore" @refresherrefresh="onRefresh"
        :refresher-enabled="true" :refresher-triggered="refresherTriggered" enhanced :bounces="false"
        :show-scrollbar="false" v-if="postList.length > 0">
        <view class="post-item" v-for="(post, index) in postList" :key="index">
          <!-- 用户信息 -->
          <view class="user-info">
            <image :src="post.avatar" class="user-avatar" />
            <text class="user-name">{{ post.userName }}</text>
          </view>
          <!-- 动态内容 -->
          <view class="post-content" @click="goToDetail(post)">
            <text class="post-text">{{ post.content }}</text>
            <!-- 图片网格 -->
            <view class="image-grid" v-if="post.images && post.images.length > 0">
              <image v-for="(img, imgIndex) in post.images" :key="imgIndex" :src="img" mode="aspectFill"
                @click.stop="previewImage(post.images, imgIndex)" />
            </view>
          </view>
          <!-- 时间和地点 -->
          <view class="post-meta" @click="goToDetail(post)">
            <text class="post-time">{{ post.time }}</text>
            <view class="location-info">
              <image src="/static/common/post.png" class="location-icon" />
              <text class="post-location">{{ post.location }}</text>
            </view>
          </view>
          <!-- 互动按钮 -->
          <view class="post-actions">
            <view class="action-item" @click.stop="toggleLike(index, post)">
              <image :src="post.isLike ? '/static/common/collect-active.png' : '/static/common/collect.png'"
                class="action-icon" />
              <text class="action-text">{{ post.likes }}</text>
            </view>
            <view class="action-item" @click.stop="goToDetail(post)">
              <image src="/static/common/comment.png" class="action-icon" />
              <text class="action-text">{{ post.comments }}</text>
            </view>
            <view class="action-item">
              <button open-type="share" :id="post.id">
                <image src="/static/common/share.png" class="action-icon" />
                <text class="action-text">{{ post.shares }}</text>
              </button>
            </view>
          </view>
        </view>
        <!-- <u-loadmore :status="status" /> -->
      </scroll-view>
      <!-- 暂无数据 -->
      <view class="no-data" v-if="!postList.length && !isDataLoading">
        <image src="/static/common/nodata.png" class="no-data-image" mode="aspectFit"></image>
      </view>

      <!-- 悬浮发布按钮 -->
      <view class="float-btn" @click="goToRelease" v-if="canSendCommunity">
        <image src="/static/common/toRelease.png" class="float-btn-icon" />
      </view>
    </view>
    <c-tabbar :selected="2"></c-tabbar>
  </view>
</template>
<script>
export default {
  data() {
    return {
      postList: [],
      canSendCommunity: false, // 是否可以发布社区动态
      pagination: {
        pageNum: 1,
        pageSize: 10
      },
      status: 'loadmore', // 'loadmore', 'loading', 'nomore'
      isDataLoading: false,
      refresherTriggered: false, // 下拉刷新状态
    };
  },
  async onShareAppMessage(i) {
    //这里调用接口
    const Share = await this.$api.newsDetailForward({
      newsId: i.target.id,
      type: 1 // 1-分享
    });
    console.log('分享结果:', Share);
    return {
      title: '木风未来',
      path: '/pages/community/detail',
      success: (res) => {
        console.log('分享成功', res);
        uni.showToast({
          title: '分享成功',
          icon: 'none'
        });

      },
      fail: (err) => {
        uni.showToast({
          title: '分享失败',
          icon: 'none'
        });
        console.error('分享失败', err);
      }
    };
  },
  onShow() {
    // 获取系统信息 - 如果需要自定义导航栏时使用
    // const systemInfo = uni.getSystemInfoSync();
    // this.statusHeight = systemInfo.statusBarHeight || 0;
    // this.navHeight = 44;

  },
  async onLoad() {

    this.$u.vuex("vuex_loading3", true);
    await this.checkSendCommunityPermission();
    await this.getPostList(true);
    this.$nextTick(() => {
      setTimeout(() => {
        this.$u.vuex("vuex_loading3", false);
      }, 1000);
    });
  },
  onPullDownRefresh() {
    this.getPostList(true).finally(() => {
      uni.stopPullDownRefresh();
    });
  },
  methods: {
    // 滚动到底部加载更多
    loadMore() {
      if (this.status !== 'nomore' && this.status !== 'loading') {
        this.getPostList();
      }
    },
    // 下拉刷新
    onRefresh() {
      this.refresherTriggered = true;
      this.$u.vuex("vuex_loading3", true);
      this.getPostList(true).finally(() => {
        setTimeout(() => {
          this.$u.vuex("vuex_loading3", false);
          this.refresherTriggered = false;
        }, 1000);
      });
    },
    // 获取帖子列表
    async getPostList(isRefresh = false) {
      if (this.isDataLoading) return;

      if (isRefresh) {
        this.pagination.pageNum = 1;
        this.postList = [];
        this.status = 'loadmore';
      }

      if (this.status === 'nomore') return;

      this.isDataLoading = true;
      this.status = 'loading';

      try {
        const res = await this.$api.newsList(this.pagination);
        const data = res.data;

        const processedList = data.list.map(item => {
          let imageArray = [];
          if (item.images) {
            try {
              const parsedImages = JSON.parse(item.images);
              imageArray = parsedImages.map(img => {
                return img.startsWith('http') ? img : this.vuex_imgUrl + img;
              });
            } catch (error) {
              console.log('图片解析错误:', error);
              imageArray = [];
            }
          }
          let avatarUrl = item.avatar;
          if (avatarUrl && !avatarUrl.startsWith('http')) {
            avatarUrl = this.vuex_imgUrl + avatarUrl;
          }
          return {
            id: item.id,
            userName: item.nickname,
            avatar: avatarUrl || 'https://picsum.photos/80/80?random=1',
            content: item.title,
            images: imageArray,
            time: this.$u.timeFormat(item.createTime, 'yyyy/mm/dd hh:MM'),
            location: item.address,
            likes: item.likeNum || 0,
            comments: item.commentNum || 0,
            shares: 0,
            isLike: item.isLike || false
          };
        });

        if (isRefresh) {
          this.postList = processedList;
        } else {
          this.postList = this.postList.concat(processedList);
        }

        if (processedList.length < this.pagination.pageSize) {
          this.status = 'nomore';
        } else {
          this.status = 'loadmore';
          this.pagination.pageNum++;
        }

      } catch (err) {
        console.error("getPostList error", err);
        this.status = 'loadmore';
      } finally {
        this.isDataLoading = false;
      }
    },
    // 检查发布社区权限
    checkSendCommunityPermission() {
      try {
        const userInfo = uni.getStorageSync('userInfo');
        if (userInfo && userInfo.sendCommunityRole !== undefined) {
          this.canSendCommunity = userInfo.sendCommunityRole;
        } else {
          this.canSendCommunity = false;
        }
        console.log('发布社区权限:', this.canSendCommunity);
      } catch (error) {
        console.log('获取用户权限失败:', error);
        this.canSendCommunity = false;
      }
    },

    // 数据加载回调
    // load(e) {
    //   console.log("社区列表数据:", e);
    //   this.postList = e.list.map(item => {
    //     // 处理图片数组
    //     let imageArray = [];
    //     if (item.images) {
    //       try {
    //         // 解析 JSON 字符串格式的图片数组
    //         const parsedImages = JSON.parse(item.images);
    //         imageArray = parsedImages.map(img => {
    //           // 如果是完整URL则直接使用，否则拼接前缀
    //           return img.startsWith('http') ? img : this.vuex_imgUrl + img;
    //         });
    //       } catch (error) {
    //         console.log('图片解析错误:', error);
    //         imageArray = [];
    //       }
    //     }
    //
    //     // 处理头像URL
    //     let avatarUrl = item.avatar;
    //     if (avatarUrl && !avatarUrl.startsWith('http')) {
    //       avatarUrl = this.vuex_imgUrl + avatarUrl;
    //     }
    //
    //     return {
    //       id: item.id,
    //       userName: item.nickname,
    //       avatar: avatarUrl || 'https://picsum.photos/80/80?random=1',
    //       content: item.title,
    //       images: imageArray,
    //       time: this.$u.timeFormat(item.createTime, 'yyyy/mm/dd hh:MM'),
    //       location: item.address,
    //       likes: item.likeNum || 0,
    //       comments: item.commentNum || 0,
    //       shares: 0, // 接口中没有分享数，使用默认值
    //       isLike: item.isLike || false
    //     };
    //   });
    // },
    // 跳转到详情页
    goToDetail(post) {
      uni.navigateTo({
        url: `/pages/community/detail?id=${post.id}`
      });
    },
    // 切换点赞
    async toggleLike(index, post) {
      try {
        // 调用点赞接口
        const res = await this.$api.newsDetailForward({
          newsId: post.id,
          type: 0 // 0-点赞
        });

        console.log('点赞结果:', res);

        // 切换点赞状态
        this.postList[index].isLike = !this.postList[index].isLike;
        // 更新点赞数
        if (this.postList[index].isLike) {
          this.postList[index].likes++;
        } else {
          this.postList[index].likes--;
        }

        uni.showToast({
          title: this.postList[index].isLike ? '点赞成功' : '取消点赞',
          icon: 'success'
        });

      } catch (error) {
        console.log('点赞错误:', error);
        uni.showToast({
          title: '操作失败',
          icon: 'none'
        });
      }
    },
    // 分享动态
    async sharesPost(post) {
      // #ifdef MP-WEIXIN
      // 微信小程序使用转发功能
      uni.showToast({
        title: '请使用右上角转发功能',
        icon: 'none'
      });
      // #endif

      // #ifdef APP-PLUS
      // App端检查是否安装微信
      uni.getProvider({
        service: 'share',
        success: (res) => {
          console.log('支持的分享平台:', res.provider);
          if (res.provider.includes('weixin')) {
            this.shareToWeChat(post, 'WXSceneSession');
          } else {
            this.showShareOptions(post);
          }
        },
        fail: () => {
          this.showShareOptions(post);
        }
      });
      // #endif

      // #ifdef H5
      // H5端使用分享选项
      this.showShareOptions(post);
      // #endif
    },

    // 显示分享选项
    showShareOptions(post) {
      uni.showActionSheet({
        itemList: ['分享到微信好友', '分享到朋友圈', '复制链接'],
        success: async (res) => {
          switch (res.tapIndex) {
            case 0:
              // 分享到微信好友
              this.shareToWeChat(post, 'WXSceneSession');
              break;
            case 1:
              // 分享到朋友圈
              this.shareToWeChat(post, 'WXSceneTimeline');
              break;
            case 2:
              // 复制链接
              this.copyLink(post);
              break;
          }
        }
      });
    },

    // 分享到微信
    async shareToWeChat(post, scene) {
      try {
        await uni.share({
          provider: "weixin",
          scene: scene,
          type: 0,
          title: `${post.userName}的动态`,
          summary: post.content,
          imageUrl: post.images && post.images.length > 0 ? post.images[0] : '/static/logo.png',
          href: `pages/community/detail?id=${post.id}`,
          success: async () => {
            // 记录分享
            try {
              await this.$api.newsDetailForward({
                newsId: post.id,
                type: 1
              });

              const postIndex = this.postList.findIndex(item => item.id === post.id);
              if (postIndex !== -1) {
                this.postList[postIndex].shares++;
              }
            } catch (error) {
              console.log('记录分享错误:', error);
            }

            uni.showToast({
              title: '分享成功',
              icon: 'success'
            });
          }
        });
      } catch (error) {
        console.log('微信分享失败:', error);
        uni.showToast({
          title: '分享失败，请检查是否安装微信',
          icon: 'none'
        });
      }
    },

    // 复制链接
    async copyLink(post) {
      try {
        await uni.setClipboardData({
          data: `${post.userName}分享了一条动态：${post.content}`,
          success: async () => {
            // 记录分享
            try {
              await this.$api.newsDetailForward({
                newsId: post.id,
                type: 1
              });

              const postIndex = this.postList.findIndex(item => item.id === post.id);
              if (postIndex !== -1) {
                this.postList[postIndex].shares++;
              }
            } catch (error) {
              console.log('记录分享错误:', error);
            }

            uni.showToast({
              title: '链接已复制',
              icon: 'success'
            });
          }
        });
      } catch (error) {
        console.log('复制失败:', error);
        uni.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    },
    // 预览图片
    previewImage(images, currentIndex) {
      console.log('预览图片:', images, currentIndex);
      uni.previewImage({
        current: currentIndex,
        urls: images,
        success: (res) => {
          console.log('图片预览成功:', res);
        },
        fail: (err) => {
          console.log('图片预览失败:', err);
          uni.showToast({
            title: '图片预览失败',
            icon: 'none'
          });
        }
      });
    },
    // 跳转到发布页面
    goToRelease() {
      uni.navigateTo({
        url: '/pages/community/releasePost'
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.community-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;

  .self {
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    padding-bottom: calc(env(safe-area-inset-bottom) + 120rpx);
  }

  .nav-bar {
    font-family: 'Bold', Source Han Serif SC;
    font-weight: bold;
    font-size: 36rpx;
    color: #000000;
  }

  .main-scroll {
    flex: 1;
    min-height: 0;

    .post-item {
      background-color: #fff;
      padding: 48rpx 32rpx 24rpx 32rpx;
      border-bottom: 8rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      // 用户信息
      .user-info {
        display: flex;
        align-items: center;
        margin-bottom: 24rpx;

        .user-avatar {
          width: 72rpx;
          height: 72rpx;
          border-radius: 50%;
          margin-right: 16rpx;
        }

        .user-name {
          font-family: 'SemiBold', Source Han Serif SC;
          font-weight: 600;
          font-size: 24rpx;
          color: #000000;
        }
      }

      // 动态内容
      .post-content {
        margin-bottom: 24rpx;

        .post-text {
          font-family: 'Medium', Source Han Serif SC;
          font-weight: 500;
          font-size: 32rpx;
          color: #000000;
          line-height: 1.6;
          margin-bottom: 24rpx;
          display: block;
        }

        // 图片网格
        .image-grid {
          display: flex;
          //justify-content: space-between;
          gap: 12rpx;
          margin-bottom: 24rpx;

          image {
            width: 220rpx;
            height: 220rpx;
          }
        }
      }

      // 时间和地点
      .post-meta {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 24rpx;

        .post-time {
          font-family: 'Regular', Source Han Serif SC;
          font-weight: 400;
          font-size: 24rpx;
          color: #999999;
        }

        .location-info {
          display: flex;
          align-items: center;
          gap: 8rpx;

          .location-icon {
            width: 20rpx;
            height: 24rpx;
          }

          .post-location {
            font-family: 'Medium', Source Han Serif SC;
            font-weight: 500;
            font-size: 24rpx;
            color: #999999;
          }
        }
      }

      // 互动按钮
      .post-actions {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .action-item {
          display: flex;
          align-items: center;
          gap: 12rpx;

          button {
            background: none;
            display: flex;
            align-items: center;
            gap: 12rpx;
            padding: 0;
            border: none;
            outline: none;
            box-shadow: none;

            &::after {
              border: none;
            }
          }

          .action-icon {
            width: 36rpx;
            height: 36rpx;
          }

          .action-text {
            font-family: 'Regular', Source Han Serif SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #000000;
          }
        }
      }
    }
  }

  .no-data {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .no-data-image {
      width: 156rpx;
      height: 192rpx;
    }
  }

  // 悬浮发布按钮
  .float-btn {
    position: fixed;
    right: 32rpx;
    bottom: calc(env(safe-area-inset-bottom) + 154rpx);
    width: 88rpx;
    height: 88rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5rpx 15rpx rgba(238, 90, 36, 0.4);
    z-index: 999;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
    }

    .float-btn-icon {
      width: 100%;
      height: 100%;
    }
  }
}
</style>