<template>
    <view>
        <c-loading></c-loading>
        <view>
            <c-navBar class="topBg" :title="type ? '个人信息' : '设置信息'" backgroundColor="#fff" isTran isPerch :isBack="true"
                backIcon="/static/common/backBlack.png"></c-navBar>
        </view>
        <view class="scrollWrap">
            <!-- <scroll-view scroll-y="true" style="height: 100%"> -->
            <view class="warpCont">

                <view class="tabsWrap">
                    <view class="userTabs">
                        <view class="brUser">
                            <!-- <img src="/static/persons/bid.png" alt="" class="logs"> -->
                            <view class="title">头像</view>
                        </view>
                        <view class="brToleft" @click="chooseImage">
                            <img :src="avatarUrl" alt="" class="logs">
                            <img src="/static/persons/rigth.png" alt="" class="toRight">
                        </view>
                    </view>
                    <view class="userTabs">
                        <view class="brUser">
                            <view class="title">昵称</view>
                        </view>
                        <view class="brToleft">
                            <view></view>
                            <u--input v-model="userTabData.nickname" placeholder="请输入" class="contY" inputAlign="right"
                                border="none" />
                            <img src="/static/persons/rigth.png" alt="" class="toRight">
                        </view>
                    </view>

                    <view class="userTabs" v-if="type">
                        <view class="brUser">
                            <view class="title">手机号码</view>
                        </view>
                        <view class="brToleft">
                            <view></view>
                            <u--input v-model="userTabData.phone" placeholder="请输入" class="contY" inputAlign="right"
                                border="none" disabled disabledColor='#fff' />
                        </view>
                    </view>
                    <!-- <view class="userTabs" @click="logout" v-if="type">
                        <view class="brUser">
                            <view class="title">退出登录</view>
                        </view>
                        <view class="brToleft">
                            <view></view>
                            <img src="/static/persons/rigth.png" alt="" class="toRight">
                        </view>
                    </view> -->
                </view>
            </view>
            <!-- </scroll-view> -->
            <view class="reBtn1" @click="saveInfo" v-if="!type">保存</view>
            <view class="reBtn1" @click="logout" v-if="type">退出登录</view>
            <view></view>
        </view>
    </view>
</template>

<script>
export default {
    components: {
    },
    data() {
        return {
            items: Array(15).fill(null),
            userTabData: {},
            type: ''
        }
    },
    computed: {
        // 头像完整URL
        avatarUrl() {
            if (this.userTabData.avatar) {
                // 如果是 http 开头的完整 URL，直接使用
                if (this.userTabData.avatar.startsWith('http')) {
                    return this.userTabData.avatar;
                }
                // 否则拼接 vuex_imgUrl
                return this.vuex_imgUrl + this.userTabData.avatar;
            }
            return '/static/persons/user.png';
        }
    },
    onLoad(i) {
        if (i) this.type = i.type === 'true'
        this.getUserInfo();
    },

    methods: {
        getUserInfo() {
            this.$api.userInfo().then(res => {
                console.log('用户信息', res);
                this.userTabData = res.data;
                this.userTabData.phone = this.userTabData.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
            }).catch(err => {
                console.log(err);
            });
        },
        saveInfo() {
            if (!this.userTabData.nickname) {
                uni.showToast({
                    title: '请输入昵称',
                    icon: 'none',
                    duration: 2000
                });
            }
            if (!this.userTabData.avatar) {
                uni.showToast({
                    title: '请上传头像',
                    icon: 'none',
                    duration: 2000
                });
            }


            // if (!this.userTabData.phone) {
            //     uni.showToast({
            //         title: '请输入手机号码',
            //         icon: 'none',
            //         duration: 2000
            //     });
            // } else {
            //     const phoneRegex = /^(1[3-9])\d{9}$/;

            //     if (!phoneRegex.test(this.userTabData.phone)) {
            //         uni.showToast({
            //             title: '请填写正确的手机号',
            //             icon: 'none',
            //             duration: 2000
            //         });
            //         return
            //     }
            // }
            delete this.userTabData.phone
            this.$api.userUpdate(this.userTabData).then(res => {
                if (res.code == 200) {  // 保存成功
                    uni.showToast({
                        title: '保存成功',
                        icon: 'none',
                        duration: 2000
                    });
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1000);
                } else {
                    uni.showToast({
                        title: '保存失败',
                        icon: 'none',
                        duration: 2000
                    });
                }

            }).catch(err => {
                console.log(err);
            });
        },
        chooseImage() {
            uni.chooseImage({
                count: 1, // 选择一张图片
                success: (res) => {
                    const tempFilePaths = res.tempFilePaths;
                    console.log("选择的图片路径:", tempFilePaths);
                    this.uploadImage(tempFilePaths[0]); // 调用上传图片函数
                },
                fail: (err) => {
                    console.error("选择图片失败", err);
                }
            });
        },
        // 上传图片
        uploadImage(filePath) {
            uni.uploadFile({
                url: this.vuex_baseUrl + '/common/upload',
                filePath: filePath,
                name: 'file',
                formData: {
                    user: 'test'
                },
                success: (res) => {
                    console.log(JSON.parse(res.data).fileName, "res")
                    this.userTabData.avatar = JSON.parse(res.data).fileName
                }
            });
        },
        logout() {
            uni.showModal({
                title: '提示',
                content: '确定要退出登录吗？',
                success: (res) => {
                    if (res.confirm) {
                        uni.clearStorageSync();
                        uni.reLaunch({
                            url: '/pages/tabbar/login'
                        });
                    }
                }
            });
        }
    }
}



</script>

<style lang="scss" scoped>
.scrollWrap {
    // position: absolute;
    // bottom: 24rpx;
    width: 100%;
    // top: 460rpx;
    border-radius: 16rpx;
    height: 90vh;
    background: #F7F7F7;

    .warpCont {
        // width: 702rpx;
        height: 100vh;
        background: #fff;
        // position: absolute;
        // top: -18vh;
        // left: 50%;
        // transform: translate(-50%);

        // border-radius: 12rpx;

        .tabsWrap {
            padding: 24rpx 24rpx 0 24rpx;


            .userTabs {
                width: 654rpx;
                height: 100rpx;
                border-bottom: 1rpx solid #E6E9F0;
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin: auto;

                .brUser {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .logs {
                        width: 32rpx;
                        height: 32rpx;
                    }

                    .title {
                        font-family: PingFang SC, PingFang SC;
                        font-weight: 500;
                        font-size: 28rpx;
                        color: #333;
                        line-height: 38rpx;
                        text-align: left;
                        font-style: normal;
                        text-transform: none;
                        margin-left: 16rpx;
                    }
                }

                .brToleft {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .logs {
                        width: 80rpx;
                        height: 80rpx;
                        border-radius: 50%;

                    }

                    .toRight {
                        width: 24rpx;
                        height: 24rpx;
                        margin-left: 14rpx;
                    }

                    .contY {
                        font-size: 24rpx;
                        color: #999999;
                    }
                }

            
            }


        }
    }
    .reBtn1 {
        width: 686rpx;
        height: 88rpx;
        background: #9E2721;
        border-radius: 70rpx;
        font-family: "Source Han Serif SC", "Source Han Serif", serif;
        font-weight: bold;
        font-size: 32rpx;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: center;
        position: fixed;
        bottom: calc(env(safe-area-inset-bottom) + 24rpx);
        left: 50%;
        transform: translateX(-50%);
    }
}

.reBtn1 {
    width: 686rpx;
    height: 88rpx;
    background: #9E2721;
    border-radius: 70rpx;
    font-family: "Source Han Serif SC", "Source Han Serif", serif;
    font-weight: bold;
    font-size: 32rpx;
    color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    position: fixed;
    bottom: calc(env(safe-area-inset-bottom) + 24rpx);
    left: 50%;
    transform: translateX(-50%);
}
</style>