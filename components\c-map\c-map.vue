<template>
    <view class="map-content">
        <map
            id="map"
            :style="mapStyle"
            :latitude="latitude"
            :longitude="longitude"
            :scale="scale"
            :markers="markers"
            :show-location="showLocation"
            :offset="mapOffset"
            @markertap="onMarkerTap"
        >
            <cover-view slot="callout">
                <!-- 自定义气泡 -->
                <cover-view v-for="(bubble, index) in markers" :key="index" @tap="onBubbleTap(bubble)">
                    <cover-view :marker-id="bubble.id">
                        <cover-view class="callout-content">
                            <cover-view class="callout-info flex-1">
                                <cover-view class="callout-title flex align-center">
                                    <cover-image class="callout-avatar" :src="bubble.avatar"></cover-image>
                                    <cover-view class="callout-title__text mf-weight-bold">
                                        {{ bubble.title }}
                                    </cover-view>
                                    <cover-view v-if="bubble.tag" class="callout-tag mf-font-20">{{ bubble.tag }}</cover-view>
                                </cover-view>
                                <cover-view class="callout-desc">
                                    {{ bubble.description }}
                                </cover-view>
                            </cover-view>
                        </cover-view>
                        <cover-view class="callout-arrow">▼</cover-view>
                    </cover-view>
                </cover-view>
            </cover-view>
        </map>
    </view>
</template>

<script>
export default {
    name: "CMap",
    props: {
        // 地图中心纬度
        latitude: {
            type: Number,
            default: 30.6586,
        },
        // 地图中心经度
        longitude: {
            type: Number,
            default: 104.0647,
        },
        // 地图缩放级别
        scale: {
            type: Number,
            default: 16,
        },
        // 标记点数据
        markers: {
            type: Array,
            default: () => [],
        },
        // 是否显示当前位置
        showLocation: {
            type: Boolean,
            default: true,
        },
        // 地图样式
        mapStyle: {
            type: String,
            default: "width: 100%; height: 220rpx",
        },
    },
    data() {
        return {
            // 地图偏移量，让标记点向下偏移20rpx，这样气泡能完全显示
            mapOffset: {
                x: 0,
                y: 80,
            },
        };
    },
    methods: {
        // 气泡点击事件
        onBubbleTap(bubble) {
            this.$emit("bubble-tap", bubble);
        },
        // 地图标记点击事件
        onMarkerTap(e) {
            this.$emit("marker-tap", e);
        },
    },
};
</script>

<style lang="scss" scoped>
.map-content {
    position: relative;
    width: 100%;
    height: 100%;

    .callout-content {
        background: #fff;
        border-radius: 16rpx;
        padding: 18rpx 10rpx;
        width: 240rpx; // 增加宽度给描述文字更多空间
        max-height: 140rpx; // 增加高度以容纳换行文字
        display: flex;
        align-items: flex-start; // 改为顶部对齐，避免文字被压缩
        gap: 20rpx;

        .callout-avatar {
            display: block;
            width: 64rpx;
            height: 24rpx;
            border-radius: 8rpx;
            background: #f5f5f5;
            flex-shrink: 0; // 防止头像被压缩
            margin-right: 10rpx;
        }

        .callout-info {
            color: #fff;

            .callout-title {
                .callout-title__text {
                    font-size: 24rpx;
                    color: #000;
                }

                .callout-tag {
                    background: #ff8e0d;
                    padding: 6rpx 10rpx;
                    border-radius: 4rpx;
                    line-height: -1;
                    margin-left: 12rpx;
                }
            }

            .callout-desc {
                margin-top: 15rpx;
                font-size: 16rpx;
                color: #000;
                // 允许文字换行显示
                word-wrap: break-word;
                word-break: break-all;
                white-space: normal;
                line-height: 1.4;
                max-height: 60rpx; // 限制最大高度约3行
                overflow: hidden;
                text-align: center;
            }
        }
    }

    .callout-arrow {
        color: #fff;
        font-size: 32rpx;
        text-align: center;
        line-height: 1;
        margin: 0 auto;
        margin-top: -10rpx;
        width: 40rpx;
        height: 32rpx;
    }
}
</style>
