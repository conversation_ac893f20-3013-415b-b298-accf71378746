<template>
	<view class="complaint-container">
		<c-loading></c-loading>
		<!-- 导航栏 -->
		<section class="nav-bar">
			<c-navBar title="投诉建议" isPerch :isBack="true" backIcon="/static/common/backBlack.png">
			</c-navBar>
		</section>

		<!-- 主要内容 -->
		<view class="main-content">
			<!-- 表单区域 -->
			<view class="form-section">
				<view class="form-container">
					<!-- 姓名 -->
					<view class="form-item">
						<view class="form-label">
							<text>您的姓名</text>
							<text class="required">*</text>
						</view>
						<input class="form-input" v-model="formData.name" placeholder="请填写您的姓名"
							placeholder-class="input-placeholder" />
					</view>

					<!-- 联系电话 -->
					<view class="form-item">
						<view class="form-label">
							<text>联系电话</text>
							<text class="required">*</text>
						</view>
						<input class="form-input" v-model="formData.phone" placeholder="请填写您的联系电话"
							placeholder-class="input-placeholder" type="number" />
					</view>

					<!-- 投诉/建议 -->
					<view class="form-item">
						<view class="form-label">
							<text>投诉/建议</text>
							<text class="required">*</text>
						</view>
						<textarea class="form-textarea" v-model="formData.suggestion" placeholder="请填写您的投诉或建议"
							placeholder-class="input-placeholder" :maxlength="500" />
					</view>

					<!-- 提交按钮 -->
					<view class="submit-section">
						<button class="submit-btn" @click="submitForm">提交</button>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				formData: {
					name: '',
					phone: '',
					suggestion: ''
				}
			};
		},
		methods: {
			// 提交表单
			submitForm() {
				// 验证必填项
				if (!this.formData.name.trim()) {
					uni.showToast({
						title: '请填写您的姓名',
						icon: 'none'
					});
					return;
				}

				if (!this.formData.phone.trim()) {
					uni.showToast({
						title: '请填写联系电话',
						icon: 'none'
					});
					return;
				}

				if (!this.formData.suggestion.trim()) {
					uni.showToast({
						title: '请填写您的投诉或建议',
						icon: 'none'
					});
					return;
				}

				// 验证手机号格式
				const phoneRegex = /^1[3-9]\d{9}$/;
				if (!phoneRegex.test(this.formData.phone)) {
					uni.showToast({
						title: '请填写正确的手机号',
						icon: 'none'
					});
					return;
				}

				// 显示提交成功提示
				uni.showToast({
					title: '提交成功',
					icon: 'success',
					duration: 2000
				});

				// 延迟返回上一页
				setTimeout(() => {
					uni.navigateBack();
				}, 2000);
			}
		}
	};
</script>

<style lang="scss" scoped>
	.complaint-container {
		width: 100%;
		min-height: 100vh;
		background-color: #FFFFFF;

		.nav-bar {
			font-family: 'Bold',
			Source Han Serif SC;
			font-weight: bold;
			font-size: 36rpx;
			color: #000000;
		}

		.main-content {
			padding-top: 40rpx;
			.form-section {
				padding: 24rpx 32rpx;

				.form-container {
					width: 100%;
					box-sizing: border-box;

					.form-item {
						.form-label {
							display: flex;
							align-items: center;
							font-family: 'Bold',
							Source Han Serif SC;
							font-weight: bold;
							font-size: 24rpx;
							color: #000000;
							margin-bottom: 8rpx;

							.required {
								color: #ff4757;
								margin-left: 4rpx;
							}
						}

						.form-input {
							width: 100%;
							height: 64rpx;
							padding: 0 24rpx;
							border: 2rpx solid #666666;
							border-radius: 12rpx;
							font-size: 28rpx;
							color: #333;
							margin-bottom: 24rpx;
							box-sizing: border-box;
						}

						.form-textarea {
							width: 100%;
							height: 260rpx;
							padding: 24rpx;
							background-color: #fff;
							border: 2rpx solid #666666;
							border-radius: 12rpx;
							font-size: 28rpx;
							color: #333;
							line-height: 1.5;
							box-sizing: border-box;
						}

						.input-placeholder {
							font-family: 'Regular',
							Source Han Serif SC;
							font-weight: 400;
							font-size: 24rpx;
							color: #999999;
						}
					}

					.submit-section {
						margin-top: 88rpx;
						position: fixed;
						bottom: calc(env(safe-area-inset-bottom) + 24rpx);
						left: 0;
						right: 0;
						background: #fff;
						z-index: 100;
						display: flex;
						justify-content: center;

						.submit-btn {
							width: 686rpx;
							height: 88rpx;
							background: #9E2721;
							font-family: Source Han Serif SC,
							Source Han Serif SC;
							font-weight: bold;
							font-size: 32rpx;
							color: #FFFFFF;
							border-radius: 70rpx;
							display: flex;
							align-items: center;
							justify-content: center;

							&:active {
								opacity: 0.8;
							}
						}
					}
				}
			}
		}
	}
</style> 