<template>
    <view class="tabbar-container">
        <!-- 自定义 tabbar -->
        <view class="custom-tabbar">
            <!-- 普通 tabbar 项 -->
            <view v-for="(item, index) in tabbarList" :key="index" class="tabbar-item"
                :class="{ active: selected === index, 'mid-item': item.midButton }" @click="handleTabClick(index)">
                <!-- 中间突出按钮 -->
                <view v-if="item.midButton" class="mid-button-wrapper">
                    <view class="mid-button-bg">
                        <image class="mid-button-icon" :src="selected === index ? item.selectedIconPath : item.iconPath"
                            mode="aspectFit" />
                    </view>
                </view>
                <!-- 普通按钮 -->
                <template v-else>
                    <image class="tabbar-icon" :src="selected === index ? item.selectedIconPath : item.iconPath"
                        mode="aspectFit" />
                    <text class="tabbar-text" :class="{ 'active-text': selected === index }">{{ item.text }}</text>
                </template>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "c-tabbar",
    props: {
        selected: {
            type: Number,
            default: 0,
        },
        midButton: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            switching: false, // 防止快速点击导致的闪烁
            tabbarList: [

                {
                    "pagePath": "/pages/tabbar/home",
                    "iconPath": "/static/tabbar/unHome.png",
                    "selectedIconPath": "/static/tabbar/home.png",
                    "text": "首页"
                },
                {
                    "pagePath": "/pages/tabbar/ai",
                    "iconPath": "/static/tabbar/unAi.png",
                    "selectedIconPath": "/static/tabbar/ai.png",
                    "text": "AI+"
                },
                {
                    "pagePath": "/pages/tabbar/community",
                    "iconPath": "/static/tabbar/unCommunity.png",
                    "selectedIconPath": "/static/tabbar/community.png",
                    "text": "社区"
                },
                {
                    "pagePath": "/pages/tabbar/case",
                    "iconPath": "/static/tabbar/unCase.png",
                    "selectedIconPath": "/static/tabbar/case.png",
                    "text": "案例库"
                },
                {
                    "pagePath": "/pages/tabbar/mine",
                    "iconPath": "/static/tabbar/unMine.png",
                    "selectedIconPath": "/static/tabbar/mine.png",
                    "text": "我的"
                }
            ],
        };
    },
    methods: {
        handleTabClick(index) {
            // 防止快速点击
            if (this.switching) return;

            // 如果点击的是当前选中的tab，不执行任何操作
            if (this.selected === index) return;

            this.switching = true;

            // 发射事件给父组件
            this.$emit("change", index);

            // 如果是中间按钮，可以执行特殊逻辑
            if (this.tabbarList[index].midButton) {
                // 中间按钮的点击逻辑，比如打开发布页面
                uni.switchTab({
                    url: this.tabbarList[index].pagePath,
                    complete: () => {
                        // 导航完成后重置状态
                        setTimeout(() => {
                            this.switching = false;
                        }, 300);
                    }
                });
            } else {
                // 普通 tab 切换
                uni.switchTab({
                    url: this.tabbarList[index].pagePath,
                    complete: () => {
                        // 导航完成后重置状态
                        setTimeout(() => {
                            this.switching = false;
                        }, 300);
                    }
                });
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.tabbar-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 9999;
}

.custom-tabbar {
    display: flex;
    align-items: center;
    background: #ffffff;
    // border-top: 1px solid #e5e5e5;
    padding-bottom: env(safe-area-inset-bottom);
    height: 120rpx;
    position: relative;
    box-shadow: 0rpx -4rpx 8rpx 0rpx rgba(63,102,111,0.1);
    /* 防止整个 tabbar 重新渲染导致的闪烁 */
    transform: translateZ(0);
    backface-visibility: hidden;
    /* 确保布局稳定 */
    contain: layout style paint;

    // 添加内部阴影效果，增强层次感
    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2rpx;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.05), transparent);
        pointer-events: none;
    }
}

.tabbar-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10rpx 0;
    position: relative;
    /* 防止重新布局导致的闪烁 */
    min-height: 100rpx;
    /* 硬件加速 */
    transform: translateZ(0);
    /* 平滑过渡 */
    transition: all 0.2s ease;

    &.mid-item {
        // 中间项不需要额外的 padding
        padding: 0;
    }
}

.tabbar-icon {
    width: 60rpx;
    height: 60rpx;
    margin-bottom: 4rpx;
    /* 防止图标闪烁 */
    transform: translateZ(0);
    transition: transform 0.2s ease;
    /* 确保图标尺寸稳定 */
    flex-shrink: 0;
}

.tabbar-text {
    font-family: 'SemiBold', Source Han Serif SC, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 600;
    font-size: 20rpx;
    color: #949494;
    line-height: 1;
    /* 防止文字闪烁的优化 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    /* 确保文字不会重新布局 */
    white-space: nowrap;
    /* 平滑过渡 */
    transition: color 0.2s ease;
    /* 强制硬件加速 */
    transform: translateZ(0);
    will-change: color;

    &.active-text {
        color: #060606;
    }
}

// 中间突出按钮样式
.mid-button-wrapper {
    position: relative;
    top: -5rpx; // 向上突出
    display: flex;
    align-items: center;
    justify-content: center;
}

.mid-button-bg {
    padding: 10rpx;
    background: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mid-button-icon {
    width: 100rpx;
    height: 100rpx;
}

// 激活状态
.tabbar-item.active:not(.mid-item) {
    .tabbar-icon {
        transform: scale(1.1);
        transition: transform 0.2s ease;
    }
}
</style>
