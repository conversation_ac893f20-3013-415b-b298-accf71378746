<template>
    <view class="detail-container">
        <c-loading2></c-loading2>
        <section class="nav-bar">
            <c-navBar title="动态详情" isPerch :isBack="true" backIcon="/static/common/backBlack.png"> </c-navBar>
        </section>
        <!-- 内容区域 -->
        <scroll-view class="main-scroll" scroll-y @scrolltolower="loadMore" :lower-threshold="50"  enhanced :bounces="false" :show-scrollbar="false">
            <!-- 动态详情 -->
            <view class="post-detail">
                <!-- 动态内容 -->
                <view class="post-content">
                    <view class="post-text">{{ postDetail.content }}</view>
                    <view class="post-text-detail">{{ postDetail.detailContent }}</view>

                    <!-- 图片显示 -->
                    <view class="detail-image" v-if="postDetail.images && postDetail.images.length > 0">
                        <image :src="postDetail.images[0]" class="main-image" mode="widthFix" @click="previewImage(postDetail.images, 0)" />
                    </view>
                </view>

                <!-- 时间和地点 -->
                <view class="post-meta">
                    <view class="post-time">{{ postDetail.time }}</view>
                    <view class="location-info">
                        <image src="/static/common/post.png" class="location-icon" />
                        <view class="post-location">{{ postDetail.location }}</view>
                    </view>
                </view>

                <!-- 互动按钮 -->
                <view class="post-actions">
                    <view class="action-item" @click="toggleLike">
                        <image
                            :src="postDetail.isLike ? '/static/common/collect-active.png' : '/static/common/collect.png'"
                            class="action-icon"
                        />
                        <view class="action-text">{{ postDetail.likes }}</view>
                    </view>
                    <view class="action-item" @click="scrollToComments">
                        <image src="/static/common/comment.png" class="action-icon" />
                        <view class="action-text">{{ postDetail.comments }}</view>
                    </view>
                    <view class="action-item">
                        <button open-type="share">
                            <image src="/static/common/share.png" class="action-icon" />
                            <view class="action-text">{{ postDetail.shares }}</view>
                        </button>
                    </view>
                </view>
            </view>

            <!-- 评论区 -->
            <view class="comments-section">
                <view class="comments-header">
                    <view class="comments-title">
                        <view>评论</view>
                        <view class="comment-count">{{ commentList.length }}</view>
                    </view>
                </view>

                <view class="comment-list" v-for="(comment, index) in commentList" :key="index">
                    <view class="comment-item">
                        <image :src="comment.avatar" class="comment-avatar" />
                        <view class="comment-content">
                            <view class="comment-header">
                                <view class="comment-name">{{ comment.userName }}</view>
                                <view class="comment-time">{{ comment.time }}</view>
                            </view>
                            <view class="comment-text" @click="replyToComment(comment, index)">{{ comment.content }}</view>

                            <!-- 回复列表 -->
                            <view class="reply-section" v-if="comment.replyList && comment.replyList.length > 0">
                                <view
                                    class="reply-item"
                                    v-for="(reply, replyIndex) in comment.visibleReplies"
                                    :key="replyIndex"
                                    @click="replyToReply(reply, comment, index)"
                                >
                                    <text class="reply-text">
                                        <text class="reply-user">{{ reply.userName }}</text>
                                        <text v-if="reply.replyToUser" class="reply-to"
                                            >回复<text class="reply-target">{{ reply.replyToUser }}</text
                                            >：</text
                                        >
                                        <text v-else class="reply-content">：</text>
                                        <text class="reply-content">{{ reply.content }}</text>
                                    </text>
                                </view>

                                <!-- 查看更多回复 -->
                                <view
                                    class="show-more-replies"
                                    v-if="comment.replyList.length > 2 && !comment.showAllReplies"
                                    @click="showAllReplies(index)"
                                >
                                    <view class="more-text">
                                        <view class="texts"> 查看更多({{ comment.replyList.length - 2 }})> </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 加载更多提示 -->
            <view class="load-more" v-if="hasMore || loading">
                <view class="loading" v-if="loading">
                    <text>加载中...</text>
                </view>
                <view class="no-more" v-else-if="!hasMore">
                    <text>没有更多了</text>
                </view>
            </view>
        </scroll-view>

        <!-- 评论输入框 - 固定在底部 -->
        <view class="comment-input-section">
            <view class="comment-input-warp" @click="showCommentInput">
                <image src="/static/common/say.png" class="say-icon" />
                <view class="comment-placeholder">说点什么吧！</view>
            </view>
        </view>
        <!-- 评论输入弹窗 -->
        <view class="comment-popup" v-if="showInputPopup" @click="hideCommentInput">
            <view class="comment-input-container" @click.stop>
                <view class="input-content">
                    <textarea
                        class="comment-textarea"
                        v-model="commentContent"
                        :placeholder="replyInfo.isReply ? `回复 ${replyInfo.targetUser}` : '说点什么吧...'"
                        :focus="showInputPopup"
                        confirm-type="send"
                        :show-confirm-bar="false"
                        :cursor-spacing="26"
                        @confirm="publishComment"
                        @blur="onInputBlur"
                        auto-height
                        :maxlength="500"
                    />
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            // api: this.$api.detailComment,
            // apiParams: {
            //   "newsId": 1,
            //   "pageNum": 1,
            //   "pageSize": 10
            // },
            postDetail: {
                id: "",
                userName: "",
                avatar: "",
                content: "",
                detailContent: "",
                mainImage: "",
                time: "",
                location: "",
                likes: 0,
                comments: 0,
                shares: 0,
                isLike: false,
            },
            commentList: [],
            showInputPopup: false,
            commentContent: "",
            replyInfo: {
                isReply: false,
                parentId: null,
                targetUser: "",
                commentIndex: -1,
                replyType: "new", // new: 新评论, main: 回复主评论, reply: 回复回复
                mainCommentId: null,
            },
            // 分页相关
            currentPage: 1,
            pageSize: 10,
            hasMore: true,
            loading: false,
        };
    },
    async onShareAppMessage() {
        //这里调用接口
        const Share = await this.$api.newsDetailForward({
            newsId: this.postDetail.id,
            type: 1, // 1-分享
        });
        console.log("分享结果:", Share);
        return {
            title: "木风未来",
            path: "/pages/community/detail",
            success: (res) => {
                console.log("分享成功", res);
                uni.showToast({
                    title: "分享成功",
                    icon: "none",
                });
            },
            fail: (err) => {
                uni.showToast({
                    title: "分享失败",
                    icon: "none",
                });
                console.error("分享失败", err);
            },
        };
    },
    onLoad(options) {
        // 显示loading
        this.$u.vuex("vuex_loading2", true);

        if (options.id) {
            this.postDetail.id = options.id;
            this.loadPostDetail(options.id);
            this.getCommentList(true); // 初始加载评论
        }

        // 延迟隐藏loading
        this.$nextTick(() => {
            setTimeout(() => {
                this.$u.vuex("vuex_loading2", false);
            }, 1000);
        });
    },
    methods: {
        load(e) {
            this.processCommentData(e.list);
            console.log("评论,", e.list);
        },
        // 加载动态详情
        async loadPostDetail(id) {
            try {
                const res = await this.$api.newsDetail(id);
                console.log("动态详情数据:", res);
                this.processDetailData(res.data);
            } catch (error) {
                console.log("动态详情错误:", error);
                uni.showToast({
                    title: "加载失败",
                    icon: "none",
                });
            }
        },
        // 处理详情数据
        processDetailData(data) {
            // 处理图片数组
            let imageArray = [];
            if (data.images) {
                try {
                    // 解析 JSON 字符串格式的图片数组
                    const parsedImages = JSON.parse(data.images);
                    imageArray = parsedImages.map((img) => {
                        // 如果是完整URL则直接使用，否则拼接前缀
                        return img.startsWith("http") ? img : this.vuex_imgUrl + img;
                    });
                } catch (error) {
                    console.log("图片解析错误:", error);
                    imageArray = [];
                }
            }

            // 处理头像URL
            let avatarUrl = data.avatar;
            if (avatarUrl && !avatarUrl.startsWith("http")) {
                avatarUrl = this.vuex_imgUrl + avatarUrl;
            }

            // 更新详情数据
            this.postDetail = {
                id: data.id,
                userName: data.nickname,
                avatar: avatarUrl,
                content: data.title,
                detailContent: data.detail,
                images: imageArray, // 添加图片数组
                mainImage: imageArray.length > 0 ? imageArray[0] : "",
                time: this.$u.timeFormat(data.createTime, "yyyy/mm/dd hh:MM"),
                location: data.address,
                likes: data.likeNum || 0,
                comments: data.commentNum || 0,
                shares: data.shareNum || 0,
                isLike: data.isLike || false,
            };
        },
        // 切换点赞
        async toggleLike() {
            try {
                // 调用点赞接口
                const res = await this.$api.newsDetailForward({
                    newsId: this.postDetail.id,
                    type: 0, // 0-点赞
                });

                console.log("点赞结果:", res);

                // 切换点赞状态
                this.postDetail.isLike = !this.postDetail.isLike;
                // 更新点赞数
                if (this.postDetail.isLike) {
                    this.postDetail.likes++;
                } else {
                    this.postDetail.likes--;
                }

                uni.showToast({
                    title: this.postDetail.isLike ? "点赞成功" : "取消点赞",
                    icon: "success",
                });
            } catch (error) {
                console.log("点赞错误:", error);
                uni.showToast({
                    title: "操作失败",
                    icon: "none",
                });
            }
        },
        // 滚动到评论区
        scrollToComments() {
            // 这里可以添加滚动到评论区的逻辑
            console.log("滚动到评论区");
        },

        // 处理评论数据
        processCommentData(commentData) {
            this.commentList = commentData.map((comment) => {
                // 处理主评论
                const mainComment = this.formatComment(comment);

                // 递归扁平化所有回复
                const flatReplies = this.flattenReplies(comment.replyList || []);

                // 创建回复映射，用于查找回复目标
                const replyMap = new Map();
                // 先添加主评论到映射中
                replyMap.set(comment.commentId, comment);
                // 再添加所有回复到映射中
                flatReplies.forEach((reply) => {
                    replyMap.set(reply.commentId, reply);
                });

                // 处理回复列表，添加回复目标信息
                const replyList = flatReplies.map((reply) => {
                    const replyComment = this.formatComment(reply);

                    // 根据parentId查找回复目标用户
                    let replyToUser = "";
                    if (reply.parentId) {
                        if (reply.parentId === comment.commentId) {
                            // 直接回复主评论，不显示回复目标
                            replyToUser = "";
                        } else {
                            // 回复其他回复，显示回复目标
                            const targetReply = replyMap.get(reply.parentId);
                            if (targetReply) {
                                replyToUser = targetReply.userName;
                            }
                        }
                    }

                    return {
                        ...replyComment,
                        replyToUser: replyToUser,
                    };
                });

                mainComment.replyList = replyList;
                mainComment.visibleReplies = replyList.slice(0, 2); // 默认显示前2条
                mainComment.showAllReplies = false;

                return mainComment;
            });
        },

        // 递归扁平化回复列表
        flattenReplies(replyList) {
            const result = [];

            const processReplies = (replies) => {
                replies.forEach((reply) => {
                    result.push(reply);
                    // 如果这个回复还有子回复，递归处理
                    if (reply.replyList && reply.replyList.length > 0) {
                        processReplies(reply.replyList);
                    }
                });
            };

            processReplies(replyList);
            return result;
        },

        // 格式化单个评论
        formatComment(comment) {
            let avatarUrl = comment.userAvatar;
            if (avatarUrl && !avatarUrl.startsWith("http")) {
                avatarUrl = this.vuex_imgUrl + avatarUrl;
            }

            return {
                commentId: comment.commentId,
                userName: comment.userName,
                avatar: avatarUrl || "https://picsum.photos/60/60?random=1",
                content: comment.content,
                time: this.$u.timeFormat(comment.createTime, "mm-dd hh:MM"),
                userId: comment.userId,
                parentId: comment.parentId || null,
            };
        },
        // 查找回复目标用户
        findReplyTargetUser(replyList, parentId) {
            if (!parentId) return "";

            // 在回复列表中查找父级评论
            const targetReply = replyList.find((reply) => reply.commentId === parentId);
            return targetReply ? targetReply.userName : "";
        },
        // 显示所有回复
        showAllReplies(commentIndex) {
            this.commentList[commentIndex].showAllReplies = true;
            this.commentList[commentIndex].visibleReplies = this.commentList[commentIndex].replyList;
        },
        // 显示评论输入框（说点什么吧）
        showCommentInput() {
            this.replyInfo = {
                isReply: false, // 新评论，不是回复
                parentId: null,
                targetUser: "",
                commentIndex: -1,
                replyType: "new", // 新评论
            };
            this.commentContent = "";
            this.showInputPopup = true;
        },
        // 回复主评论
        replyToComment(comment, commentIndex) {
            this.replyInfo = {
                isReply: true,
                parentId: comment.commentId, // 传递主评论的commentId
                targetUser: comment.userName,
                commentIndex: commentIndex,
                replyType: "main", // 回复主评论
            };
            this.commentContent = "";
            this.showInputPopup = true;
        },

        // 回复回复
        replyToReply(reply, mainComment, commentIndex) {
            this.replyInfo = {
                isReply: true,
                parentId: reply.commentId, // 传递被回复评论的commentId
                targetUser: reply.userName,
                commentIndex: commentIndex,
                replyType: "reply", // 回复回复
                mainCommentId: mainComment.commentId, // 主评论ID
            };
            this.commentContent = "";
            this.showInputPopup = true;
        },
        // 隐藏评论输入框
        hideCommentInput() {
            this.showInputPopup = false;
            this.commentContent = "";
        },
        // 输入框失焦
        onInputBlur() {
            // 延迟隐藏，避免点击发送按钮时输入框提前隐藏
            setTimeout(() => {
                // 微信朋友圈样式：失焦时自动隐藏输入框
                this.hideCommentInput();
            }, 200);
        },
        // 发布评论
        async publishComment() {
            if (!this.commentContent.trim()) {
                uni.showToast({
                    title: "请输入评论内容",
                    icon: "none",
                });
                return;
            }

            try {
                const params = {
                    content: this.commentContent.trim(),
                    newsId: this.postDetail.id,
                };

                // 处理回复逻辑
                if (this.replyInfo.isReply) {
                    // 回复任何评论时，传递被回复评论的commentId
                    params.parentId = this.replyInfo.parentId;
                }
                // 注意：点击"说点什么吧"发表新评论时不传parentId，后端会自动设为null

                console.log("发布评论参数:", params);
                const res = await this.$api.commentPublish(params);
                console.log("发布评论结果:", res);

                uni.showToast({
                    title: "评论成功",
                    icon: "success",
                });

                // 重新加载评论列表
                this.getCommentList(true);

                // 隐藏输入框
                this.hideCommentInput();
            } catch (error) {
                console.log("发布评论错误:", error);
                uni.showToast({
                    title: "评论失败",
                    icon: "none",
                });
            }
        },

        // 预览图片
        previewImage(images, currentIndex) {
            console.log("预览图片:", images, currentIndex);
            uni.previewImage({
                current: currentIndex,
                urls: images,
                success: (res) => {
                    console.log("图片预览成功:", res);
                },
                fail: (err) => {
                    console.log("图片预览失败:", err);
                    uni.showToast({
                        title: "图片预览失败",
                        icon: "none",
                    });
                },
            });
        },

        // 获取评论列表
        async getCommentList(isRefresh = false) {
            if (this.loading) return;

            if (isRefresh) {
                this.currentPage = 1;
                this.hasMore = true;
                this.commentList = [];
            }

            if (!this.hasMore) return;

            this.loading = true;

            try {
                const params = {
                    newsId: this.postDetail.id,
                    pageNum: this.currentPage,
                    pageSize: this.pageSize,
                };

                console.log("获取评论列表参数:", params);
                const res = await this.$api.detailComment(params);
                console.log("评论列表结果:", res);

                if (res.code === 200 && res.data) {
                    const newComments = res.data.list || [];

                    if (isRefresh) {
                        // 刷新时清空现有数据，直接处理新数据
                        this.processCommentData(newComments);
                    } else {
                        // 追加时需要保存当前数据
                        const currentList = [...this.commentList];
                        this.processCommentData(newComments);
                        // 将新处理的数据追加到原有数据后面
                        this.commentList = [...currentList, ...this.commentList];
                    }

                    // 更新分页状态
                    this.currentPage++;
                    this.hasMore = newComments.length === this.pageSize;
                } else {
                    this.hasMore = false;
                }
            } catch (error) {
                console.log("获取评论列表失败:", error);
                uni.showToast({
                    title: "获取评论失败",
                    icon: "none",
                });
            } finally {
                this.loading = false;
            }
        },

        // 加载更多
        loadMore() {
            if (!this.loading && this.hasMore) {
                this.getCommentList(false);
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.detail-container {
    width: 100%;
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .nav-bar {
        font-family: "Bold", Source Han Serif SC;
        font-weight: bold;
        font-size: 36rpx;
        color: #000000;
        flex-shrink: 0;
    }

    .content-wrapper {
        // flex: 1;
        overflow: hidden;
        padding-bottom: calc(112rpx + env(safe-area-inset-bottom)); // 为固定输入框留出空间
    }

    .main-scroll {
        height: calc(100vh - 200rpx - env(safe-area-inset-bottom));
        padding-bottom: calc(112rpx + env(safe-area-inset-bottom)); // 为固定输入框留出空间
    }

    .page-scroll {
        height: calc(100vh - 328rpx);
    }

    // 动态详情
    .post-detail {
        background-color: #fff;
        padding: 48rpx 32rpx 24rpx 32rpx;
        border-bottom: 8rpx solid #f5f5f5;

        // 动态内容
        .post-content {
            margin-bottom: 24rpx;

            .post-text {
                font-family: "Medium", Source Han Serif SC;
                font-weight: 500;
                font-size: 32rpx;
                color: #000000;
                line-height: 1.6;
                margin-bottom: 20rpx;
                display: block;
            }

            .post-text-detail {
                font-family: "Regular", Source Han Serif SC;
                font-weight: 400;
                font-size: 28rpx;
                color: #333333;
                line-height: 1.6;
            }

            .detail-image {
                width: 100%;
                margin-top: 16rpx;

                .main-image {
                    width: 100%;
                    border-radius: 12rpx;
                }
            }
        }

        // 时间和地点
        .post-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 24rpx;

            .post-time {
                font-family: "Regular", Source Han Serif SC;
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
            }

            .location-info {
                display: flex;
                align-items: center;
                gap: 8rpx;

                .location-icon {
                    width: 20rpx;
                    height: 24rpx;
                }

                .post-location {
                    font-family: "Medium", Source Han Serif SC;
                    font-weight: 500;
                    font-size: 24rpx;
                    color: #999999;
                }
            }
        }

        // 互动按钮
        .post-actions {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .action-item {
                display: flex;
                align-items: center;
                gap: 12rpx;

                button {
                    background: none;
                    display: flex;
                    align-items: center;
                    gap: 12rpx;
                    padding: 0;
                    border: none;
                    outline: none;
                    box-shadow: none;

                    &::after {
                        border: none;
                    }
                }

                .action-icon {
                    width: 36rpx;
                    height: 36rpx;
                }

                .action-text {
                    font-family: "Regular", Source Han Serif SC;
                    font-weight: 400;
                    font-size: 28rpx;
                    color: #000000;
                }
            }
        }
    }

    // 评论区
    .comments-section {
        background-color: #fff;
        padding: 40rpx 32rpx;
        // padding-bottom: 20rpx;

        .comments-header {
            margin-bottom: 32rpx;

            .comments-title {
                display: flex;
                gap: 10rpx;
                font-family: "Regular", Source Han Serif SC;
                font-weight: 400;
                font-size: 28rpx;
                color: #1a1a1a;

                .comment-count {
                    color: #9e2722;
                    margin-top: 2rpx;
                }
            }
        }

        .comment-list {
            .comment-item {
                display: flex;
                margin-bottom: 22rpx;

                .comment-avatar {
                    width: 48rpx;
                    height: 48rpx;
                    border-radius: 50%;
                    margin-right: 16rpx;
                    flex-shrink: 0;
                }

                .comment-content {
                    flex: 1;
                    border-bottom: 1rpx solid #e3e4e5;
                    padding-bottom: 24rpx;

                    .comment-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 12rpx;
                        margin-top: 6rpx;

                        .comment-name {
                            font-family: "Regular", Source Han Serif SC;
                            font-weight: 400;
                            font-size: 24rpx;
                            color: #000000;
                        }
                    }

                    .comment-text {
                        font-family: "Regular", Source Han Serif SC;
                        font-weight: 400;
                        font-size: 24rpx;
                        color: #333333;
                        line-height: 1.5;
                        margin-bottom: 8rpx;
                    }

                    .comment-time {
                        font-family: "Regular", Source Han Serif SC;
                        font-weight: 400;
                        font-size: 20rpx;
                        color: #999999;
                        margin-bottom: 24rpx;
                    }
                }
            }
        }
    }

    // 评论输入框 - 固定在底部
    .comment-input-section {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: #fff;
        border-top: 1rpx solid #f0f0f0;
        display: flex;
        justify-content: center;
        padding: 20rpx 32rpx;
        padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
        z-index: 100;

        .comment-input-warp {
            width: 686rpx;
            height: 72rpx;
            background: #fafafa;
            border-radius: 40rpx;
            display: flex;
            justify-content: center;
            align-items: center;

            .say-icon {
                width: 24rpx;
                height: 24rpx;
            }

            .comment-placeholder {
                font-family: "Regular", Source Han Serif SC;
                font-weight: 400;
                font-size: 24rpx;
                color: #999999;
            }
        }
    }

    // 回复区域样式
    .reply-section {
        margin-top: 16rpx;
        padding: 16rpx;
        background: #fafafa;
        border-radius: 12rpx;

        .reply-item {
            margin-bottom: 8rpx;

            .reply-text {
                font-size: 24rpx;
                line-height: 1.4;

                .reply-user {
                    color: #576b95;
                    font-weight: 500;
                }

                .reply-to {
                    color: #999;
                    margin: 0 4rpx;
                }

                .reply-target {
                    color: #576b95;
                    font-weight: 500;
                }

                .reply-content {
                    color: #333;
                }
            }
        }

        .show-more-replies {
            .more-text {
                display: flex;
                align-items: center;
                font-family: "Medium", Source Han Serif SC;
                font-weight: 500;
                font-size: 24rpx;
                color: #9e2721;
            }
        }
    }

    .comment-actions {
        margin-top: 12rpx;

        .reply-btn {
            font-family: "Medium", Source Han Serif SC;
            font-weight: 500;
            font-size: 24rpx;
            color: #9e2721;
        }
    }

    // 评论输入弹窗
    .comment-popup {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.3);
        z-index: 9999;
        display: flex;
        align-items: flex-end;

        .comment-input-container {
            width: 100%;
            background-color: #fff;
            padding: 24rpx 32rpx;
            padding-bottom: calc(24rpx + env(safe-area-inset-bottom));

            .input-content {
                width: 100%;
                display: flex;
                justify-content: space-between;
                // align-items: flex-end;
                // gap: 16rpx;

                .comment-textarea {
                    width: 100%;
                    padding: 16rpx 20rpx;
                    font-size: 28rpx;
                    line-height: 1.4;
                    resize: none;
                    background-color: #f8f8f8;
                }

                .input-actions {
                    display: flex;
                    align-items: center;
                    gap: 24rpx;

                    .cancel-btn {
                        font-family: "Regular", Source Han Serif SC;
                        color: #999;
                        font-size: 22rpx;
                        padding: 12rpx 0;
                    }

                    .send-btn {
                        font-family: "Regular", Source Han Serif SC;
                        color: #fff;
                        font-size: 22rpx;
                        padding: 12rpx 24rpx;
                        border-radius: 8rpx;
                        background-color: #07c160;
                        transition: all 0.3s;
                        // &.active {
                        //   color: #fff;
                        //   background-color: #576b95;
                        // }
                    }
                }
            }
        }
    }

    // 加载更多提示
    .load-more {
        padding: 40rpx 0;
        text-align: center;

        .loading,
        .no-more {
            font-size: 28rpx;
            color: #999;
        }
    }
}
</style>
