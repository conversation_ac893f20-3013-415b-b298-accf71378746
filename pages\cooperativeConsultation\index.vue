<template>
  <view class="consultation-container">
    <c-loading></c-loading>
    <!-- 导航栏 -->
    <section class="nav-bar">
      <c-navBar title="合作咨询" isPerch :isBack="true" backIcon="/static/common/backBlack.png">
      </c-navBar>
    </section>

    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 头部图片和标题 -->
      <view class="header-section">
        <image src="/static/home/<USER>" class="header-image" mode="aspectFill" />
      </view>

      <!-- 表单区域 -->
      <view class="form-section">
        <view class="form-intro">
          <text>请完善以下信息，木风未来期待与您合作</text>
        </view>

        <view class="form-container">
          <!-- 公司名称 -->
          <view class="form-item">
            <view class="form-label">
              <text>您的公司名称</text>
              <text class="required">*</text>
            </view>
            <input class="form-input" v-model="formData.companyName" placeholder="请填写您的公司名称"
              placeholder-class="input-placeholder" />
          </view>

          <!-- 联系电话 -->
          <view class="form-item">
            <view class="form-label">
              <text>联系电话</text>
              <text class="required">*</text>
            </view>
            <input class="form-input" v-model="formData.phone" placeholder="请填写您的联系电话"
              placeholder-class="input-placeholder" type="number" />
          </view>

          <!-- 行业 -->
          <view class="form-item">
            <view class="form-label">
              <text>行业</text>
            </view>
            <input class="form-input" v-model="formData.industry" placeholder="请填写您所属行业"
              placeholder-class="input-placeholder" />
          </view>

          <!-- 城市 -->
          <view class="form-item">
            <view class="form-label">
              <text>城市</text>
            </view>
            <input class="form-input" v-model="formData.city" placeholder="请填写您所在城市"
              placeholder-class="input-placeholder" />
          </view>

          <!-- 咨询信息 -->
          <view class="form-item">
            <view class="form-label">
              <text>咨询信息</text>
              <text class="required">*</text>
            </view>
            <textarea class="form-textarea" v-model="formData.consultationInfo" placeholder="请填写您的咨询信息"
              placeholder-class="input-placeholder" :maxlength="500" />
          </view>

          <!-- 提交按钮 -->
          <view class="submit-section">
            <button class="submit-btn" @click="submitForm">提交</button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      formData: {
        companyName: '',
        phone: '',
        industry: '',
        city: '',
        consultationInfo: ''
      }
    };
  },
  methods: {
    // 提交表单
    async submitForm() {

      // 验证必填项
      if (!this.formData.companyName.trim()) {
        uni.showToast({
          title: '请填写公司名称',
          icon: 'none'
        });
        return;
      }

      if (!this.formData.phone.trim()) {
        uni.showToast({
          title: '请填写联系电话',
          icon: 'none'
        });
        return;
      }

      if (!this.formData.consultationInfo.trim()) {
        uni.showToast({
          title: '请填写咨询信息',
          icon: 'none'
        });
        return;
      }

      // 验证手机号格式
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(this.formData.phone)) {
        uni.showToast({
          title: '请填写正确的手机号',
          icon: 'none'
        });
        return;
      }

      try {


        // 调用合作咨询接口
        const params = {
          city: this.formData.city,
          companyName: this.formData.companyName,
          industry: this.formData.industry,
          info: this.formData.consultationInfo,
          phone: this.formData.phone
        };

        console.log('提交数据:', params);
        const res = await this.$api.commonConsultation(params);
        console.log('提交结果:', res);

        uni.hideLoading();

        if (res.code === 200) {
          uni.showToast({
            title: '提交成功',
            icon: 'success',
            duration: 2000
          });

          // 延迟返回上一页
          setTimeout(() => {
            uni.navigateBack();
          }, 2000);
        } else {
          uni.showToast({
            title: res.message || '提交失败，请重试',
            icon: 'none'
          });
        }

      } catch (error) {
        console.log('提交失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '提交失败，请重试',
          icon: 'none'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.consultation-container {
  width: 100%;
  min-height: 100vh;

  .nav-bar {
    font-family: 'Bold', Source Han Serif SC;
    font-weight: bold;
    font-size: 36rpx;
    color: #000000;
  }

  .main-content {
    .header-section {
      position: relative;
      width: 100%;

      .header-image {
        width: 100%;
        height: 348rpx;
      }


    }

    .form-section {
      padding: 24rpx 32rpx;

      .form-intro {
        text-align: center;
        font-family: 'Bold', Source Han Serif SC;
        font-weight: bold;
        font-size: 28rpx;
        color: #000000;
        margin-bottom: 40rpx;
        line-height: 1.5;
      }

      .form-container {
        width: 92%;

        .form-item {
          // margin-bottom: 40rpx;

          .form-label {
            display: flex;
            align-items: center;
            font-family: 'Bold', Source Han Serif SC;
            font-weight: bold;
            font-size: 24rpx;
            color: #000000;
            margin-bottom: 8rpx;

            .required {
              color: #ff4757;
              margin-left: 4rpx;
            }
          }

          .form-input {
            width: 100%;
            height: 64rpx;
            padding: 0 24rpx;
            border: 2rpx solid #666666;
            border-radius: 12rpx;
            font-size: 28rpx;
            color: #333;
            margin-bottom: 24rpx;

          }

          .form-textarea {
            width: 100%;
            height: 160rpx;
            padding: 24rpx;
            background-color: #fff;
            border-radius: 12rpx;
            border: 2rpx solid #666666;
            border-radius: 12rpx;
            font-size: 28rpx;
            color: #333;
            line-height: 1.5;
          }

          .input-placeholder {
            font-family: 'Regular', Source Han Serif SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #999999;
          }
        }

        .submit-section {
          margin-top: 88rpx;
          // padding: 0 20rpx;
          position: fixed;
          bottom: calc(env(safe-area-inset-bottom) + 24rpx);
          left: 0;
          right: 0;
          background: #fff;
          z-index: 100;

          .submit-btn {
            width: 686rpx;
            height: 88rpx;
            background: #9E2721;
            font-family: Source Han Serif SC, Source Han Serif SC;
            font-weight: bold;
            font-size: 32rpx;
            color: #FFFFFF;
            border-radius: 70rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            &:active {
              opacity: 0.8;
            }
          }
        }
      }
    }
  }
}
</style>
