<template>
    <view class="loading-box" v-show="vuex_loading3" @click="dismiss">
        <view class="loading-content">
            <image src="/static/common/loading.png" style="width: 156rpx;height: 132rpx;" />
            <!-- 使用CSS动画的三个点 -->
            <view class="loading-dots">
                <view class="dot dot1"></view>
                <view class="dot dot2"></view>
                <view class="dot dot3"></view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {};
    },

    methods: {
        dismiss() {
            // this.$u.vuex("vuex_loading", false);
        },
    },
};
</script>

<style>
.loading-box {
    width: 100%;
    /* 减去顶部导航栏和底部tabbar高度 */
    height: calc(100vh - 80px - 60px - env(safe-area-inset-bottom));
    /* background-color: rgba(0, 0, 0, 0.4); */
    background: #fff;
    backdrop-filter: blur(10px);
    position: fixed;
    left: 0;
    /* 从导航栏下方开始 */
    top: 80px;
    z-index: 10000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;


}

.loading-dots {
    margin-top: 30rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

.dot {
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    background-color: #C8C8C8;
    animation: dotPulse 1.2s ease-in-out infinite;
}

.dot1 {
    animation-delay: 0s;
}

.dot2 {
    animation-delay: 0.4s;
}

.dot3 {
    animation-delay: 0.8s;
}

@keyframes dotPulse {

    0%,
    100% {
        transform: scale(0.8);
        opacity: 0.7;
    }

    50% {
        transform: scale(1.2);
        opacity: 1;
    }
}
</style>
