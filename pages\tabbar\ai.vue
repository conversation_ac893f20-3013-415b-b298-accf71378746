<template>
	<view class="ai-container">
		<c-loading3></c-loading3>
		<!-- 自定义导航栏 -->
		<section class="nav-bar">
			<c-navBar title="AI+" isPerch :isBack="false" titleAlign="left" :titleStyle="{
				'font-family': 'Bold, Source Han Serif SC',
				'font-weight': 'bold',
				'font-size': '44rpx'
			}"> </c-navBar>
		</section>
		<!-- 页面内容 -->
		<scroll-view class="page-content" :scroll-y="false" enhanced :bounces="false" :show-scrollbar="false"
			:style="{ height: `calc(100vh - ${totalNavHeight}px)` }">
			<!-- AI技术引擎 -->
			<view class="engine-section">
				<view class="section-title">AI技术引擎</view>
				<view class="section-subtitle">自主可控的智能底层驱动力</view>
				<view class="engine-list">
					<view class="engine-item" v-for="(item, index) in engineItems" :key="index">
						<image class="item-placeholder" :src="item.image" mode="aspectFill"></image>
						<view class="item-info">
							<view class="item-title">{{ item.title }}</view>
							<view class="item-desc">{{ item.desc }}</view>
						</view>
					</view>
				</view>
			</view>
			<!-- AI应用场景 -->
			<view class="scene-section">
				<view class="section-header">
					<view class="section-title">AI应用场景</view>
					<view class="section-subtitle">多个行业的AI应用</view>
				</view>
				<scroll-view class="scene-scroll" scroll-x enhanced :bounces="false" :show-scrollbar="false">
					<view class="scene-list">
						<view class="scene-card" v-for="(item, index) in sceneItems" :key="index">
							<image class="scene-image" :src="item.image" mode="aspectFill"></image>
							<view class="scene-info">
								<view class="scene-title">{{ item.title }}</view>
								<view class="scene-desc">{{ item.desc }}</view>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</scroll-view>
		<c-tabbar :selected="1"></c-tabbar>
	</view>
</template>

<script>
import image from '../../components/c-check/image';
export default {
	data() {
		return {
			totalNavHeight: 0,
			engineItems: [{
				title: '本地模型部署',
				desc: '企业级私有化专属部署\n安全防护全面体系',
				image: "/static/home/<USER>"
			}, {
				title: '模型训练',
				desc: '在特定领域进行预训练，并使用标注数据进行微调，预训练打造适应特定领域需求的大模型',
				image: "/static/home/<USER>"
			}, {
				title: '自适应进化体系',
				desc: '流式数据自动触发模型微调 主动学习识别高价值数据',
				image: "/static/home/<USER>"
			}],
			sceneItems: [{
				title: 'AI电商',
				desc: 'AI在电商领域的应用正彻底重塑行业格局，从用户体验到运营效率带来了革命性提升',
				image: '/static/home/<USER>'
			}, {
				title: 'AI视频',
				desc: '主要围绕人工智能技术在视频内容的生成、编辑、分析、增强等环节的应用',
				image: '/static/home/<USER>'
			}]
		};
	},
	onShow() {

	},
	onLoad() {
		const systemInfo = uni.getSystemInfoSync();
		let statusHeight = systemInfo.statusBarHeight || 0;
		let navHeight = 0;
		// #ifdef MP-WEIXIN
		const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
		navHeight = menuButtonInfo.height + (menuButtonInfo.top - statusHeight) * 2;
		// #endif
		// #ifndef MP-WEIXIN
		navHeight = 44;
		// #endif
		this.totalNavHeight = statusHeight + navHeight;

		this.$u.vuex("vuex_loading3", true);
		this.$nextTick(() => {
			setTimeout(() => {
				this.$u.vuex("vuex_loading3", false);
			}, 1000);
		});
	},
	methods: {}
};
</script>

<style lang="scss" scoped>
.ai-container {
	height: 100vh;
	background-color: #fff;
	overflow: hidden;

	.page-content {
		// height: calc(100vh - 168rpx);
		padding-bottom: calc(env(safe-area-inset-bottom));
		box-sizing: border-box;

		// AI技术引擎区域
		.engine-section {
			padding: 32rpx 32rpx 0 32rpx;

			.section-title {
				font-family: "Bold", Source Han Serif SC;
				font-weight: bold;
				font-size: 36rpx;
				color: #000000;
			}

			.section-subtitle {
				font-family: 'Regular', Source Han Serif SC;
				font-weight: 400;
				font-size: 24rpx;
				color: #666666;
			}

			.engine-list {
				margin-top: 24rpx;

				.engine-item {
					display: flex;
					align-items: center;
					gap: 32rpx;

					.item-placeholder {
						width: 96rpx;
						height: 96rpx;
						// background: #D9D9D9;
					}

					.item-info {
						height: 100%;
						flex: 1;
						border-bottom: 1rpx solid #f0f0f0;
						padding: 24rpx 0;

						.item-title {
							font-family: 'Bold', Source Han Serif SC;
							font-weight: bold;
							font-size: 28rpx;
							color: #000000;
						}

						.item-desc {
							font-family: 'Regular', Source Han Serif SC;
							font-weight: 400;
							font-size: 20rpx;
							color: #333333;
							margin-top: 16rpx;
							// 超出隐藏
							overflow: hidden;
							text-overflow: ellipsis;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
						}
					}
				}
			}
		}

		// AI应用场景区域
		.scene-section {
			padding: 30rpx 0 40rpx 30rpx;

			.section-header {
				padding-right: 30rpx;

				.section-title {
					font-family: "Bold", Source Han Serif SC;
					font-weight: bold;
					font-size: 36rpx;
					color: #000000;
				}

				.section-subtitle {
					font-family: 'Regular', Source Han Serif SC;
					font-weight: 400;
					font-size: 24rpx;
					color: #666666;
				}
			}

			.scene-scroll {
				width: 100%;
				margin-top: 24rpx;
				white-space: nowrap;

				.scene-list {
					display: inline-flex;

					.scene-card {
						width: 560rpx;
						height: 540rpx;
						margin-right: 32rpx;
						border-radius: 20rpx;
						overflow: hidden;
						border: 2rpx solid #BCBBB7;
						display: inline-block;

						.scene-image {
							width: 100%;
							height: 392rpx;
							display: block;
						}

						.scene-info {
							padding: 24rpx;

							.scene-title {
								font-family: 'Bold', Source Han Serif SC;
								font-weight: bold;
								font-size: 28rpx;
								color: #000000;
							}

							.scene-desc {
								font-family: 'Regular', Source Han Serif SC;
								font-weight: 400;
								font-size: 24rpx;
								color: #333333;
								white-space: normal;
								display: -webkit-box;
								-webkit-box-orient: vertical;
								-webkit-line-clamp: 3;
								overflow: hidden;
							}
						}
					}
				}
			}
		}
	}
}
</style>