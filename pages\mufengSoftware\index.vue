<!-- 木风软件 -->

<template>
    <view class="case-detail-container">
        <c-loading></c-loading>
        <!-- <view class="case-images-warp">
            <image class="case-images" src="/static/home/<USER>" />
        </view> -->

        <scroll-view class="case-image-container" scroll-y  enhanced :bounces="false" :show-scrollbar="false">
            <view  style="padding-bottom: 50rpx;">
                <image class="case-detail-image"
                    src="https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/Slice <EMAIL>"
                    mode="widthFix" />
            </view>

        </scroll-view>

        <!-- 自定义导航栏 -->
        <view class="custom-navbar"
            :style="{ paddingTop: statusHeight + 'px', height: (statusHeight + navHeight) + 'px' }">
            <view class="navbar-content" :style="{ height: navHeight + 'px' }">
                <!-- 返回按钮 -->
                <view class="nav-left" @click="goBack">
                    <image src="/static/common/backwhite.png" class="back-icon" />
                </view>

                <!-- 标题 -->
                <view class="nav-title">
                    <text class="title-text">木风设计</text>
                </view>

                <!-- 右侧操作按钮 -->
                <view class="nav-right">
                </view>
            </view>
        </view>

        <!-- 案例信息覆盖层 -->
        <!-- <view class="case-info-overlay">
        <view class="case-title">{{ caseTitle }}</view>
        <view class="case-subtitle">{{ caseSubtitle }}</view>
      </view> -->
    </view>
</template>

<script>
export default {
    data() {
        return {
            statusHeight: 0,
            navHeight: 44,
            caseId: '',
            caseTitle: '智能家居APP',
            caseSubtitle: '通过移动设备，如智能手机',
            caseImage: ''
        };
    },
    onLoad(options) {    // 获取系统信息
        const systemInfo = uni.getSystemInfoSync();
        this.statusHeight = systemInfo.statusBarHeight || 0;

        // 获取导航栏高度
        const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
        this.navHeight = menuButtonInfo.height + (menuButtonInfo.top - this.statusHeight) * 2;
        this.navHeight = 44; // 固定高度
        // 获取传递的参数
        //   if (options.id) {
        //     this.caseId = options.id;
        //   }
        //   this.getCaseDetail()
    },

    methods: {
        async getCaseDetail() {
            try {
                const res = await this.$api.caseDetail({ id: this.caseId });
                this.caseImage = res.data.detailImage
                console.log("案例详情:", res);
            } catch (error) { console.log("案例详情:", error); }
        },

        // 返回上一页
        goBack() {
            uni.navigateBack();
        },


    }
};
</script>

<style lang="scss" scoped>
.case-detail-container {
    width: 100%;
    height: 100vh;
    position: relative;
    background: #F7F7F7;
    // padding: 68rpx 32rpx;

    .case-images {
        width: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
    }
}

/* 案例图片容器 */
.case-image-container {
    position: relative;
    // top: 138rpx;
    left: 0;
    background: #F7F7F7;
    // width: calc(100% - 64rpx);
    height: 100%;
    z-index: 2;
    // padding: 68rpx 0;
}

.case-detail-image {
    width: 100%;
    height: auto;
    display: block;
}

/* 自定义导航栏 */
.custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    background: transparent;
}

.navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx;
}

.nav-left {
    width: 80rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-icon {
    width: 40rpx;
    height: 40rpx;
}

.nav-title {
    flex: 1;
    text-align: center;
}

.title-text {
    font-size: 36rpx;
    font-weight: 500;
    color: #ffffff;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 24rpx;
    width: 80rpx;
    justify-content: flex-end;
}

.action-btn {
    width: 64rpx;
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    backdrop-filter: blur(10rpx);
}

/* 更多按钮样式 */
.more-dots {
    display: flex;
    flex-direction: column;
    gap: 4rpx;
}

.dot {
    width: 6rpx;
    height: 6rpx;
    background: #ffffff;
    border-radius: 50%;
}

/* 分享按钮样式 */
.share-icon {
    position: relative;
    width: 32rpx;
    height: 32rpx;
}

.share-circle {
    position: absolute;
    top: 8rpx;
    left: 8rpx;
    width: 16rpx;
    height: 16rpx;
    border: 2rpx solid #ffffff;
    border-radius: 50%;
}

.share-arrow {
    position: absolute;
    top: 4rpx;
    right: 4rpx;
    width: 0;
    height: 0;
    border-left: 8rpx solid #ffffff;
    border-top: 4rpx solid transparent;
    border-bottom: 4rpx solid transparent;
}

/* 案例信息覆盖层 */
.case-info-overlay {
    position: absolute;
    bottom: 100rpx;
    left: 0;
    right: 0;
    padding: 0 48rpx;
    z-index: 2;
}

.case-title {
    font-family: "Bold", Source Han Serif SC;
    font-weight: bold;
    font-size: 48rpx;
    color: #ffffff;
    margin-bottom: 16rpx;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.case-subtitle {
    font-family: "Regular", Source Han Serif SC;
    font-weight: 400;
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}
</style>