<template>
    <view class="search-page-container">
        <c-loading2></c-loading2>
        <!-- 自定义导航栏 -->
        <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
            <view class="navbar-content" :style="{ height: navBarHeight + 'px' }">
                <image class="back-icon" src="/static/common/backBlack.png" @click="goBack"></image>
                <view class="search-input-wrapper">
                    <input class="search-input" type="text" v-model="keyword" placeholder="搜索案例"
                        placeholder-style="color:#999; font-size: 28rpx;" @confirm="handleSearch" />
                    <image class="search-icon" src="/static/common/searchBlack.png" @click="handleSearch"></image>
                </view>
            </view>
        </view>

        <view class="search-content-area" :style="{ marginTop: (statusBarHeight + navBarHeight) + 'px' }">
            <!-- 搜索记录 -->
            <view class="history-section" v-if="historyList.length > 0">
                <view class="section-header">
                    <text class="section-title">搜索记录</text>
                    <text class="clear-history" @click="clearHistory">清除记录</text>
                </view>
                <view class="tag-list">
                    <text class="tag-item" v-for="(item, index) in historyList" :key="index" @click="searchByTag(item)">
                        {{ item }}
                    </text>
                </view>
            </view>

            <!-- 推荐 -->
            <view class="recommend-section">
                <view class="section-header">
                    <text class="section-title">推荐</text>
                </view>
                <view class="tag-list">
                    <text class="tag-item" v-for="(item, index) in recommendedList" :key="index"
                        @click="searchByTag(item)">
                        {{ item }}
                    </text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            keyword: '',
            historyList: [],
            recommendedList: ['供应链', '健身房'],
            statusBarHeight: 0,
            navBarHeight: 44, // 默认导航栏高度
        };
    },
    onLoad() {
        // 显示loading
        this.$u.vuex("vuex_loading2", true);

        const systemInfo = uni.getSystemInfoSync();
        this.statusBarHeight = systemInfo.statusBarHeight || 0;
        this.loadHistory();

        // 延迟隐藏loading
        this.$nextTick(() => {
            setTimeout(() => {
                this.$u.vuex("vuex_loading2", false);
            }, 1000);
        });
    },
    methods: {
        goBack() {
            uni.navigateBack();
        },
        handleSearch() {
            const searchKeyword = this.keyword.trim();
            if (!searchKeyword) {
                uni.showToast({
                    title: '请输入搜索内容',
                    icon: 'none'
                });
                return;
            }
            this.addToHistory(searchKeyword);
            // 跳转到搜索结果页面
            uni.navigateTo({
                url: `/pages/search/result?keyword=${encodeURIComponent(searchKeyword)}`
            });
        },
        searchByTag(tag) {
            this.keyword = tag;
            this.handleSearch();
        },
        addToHistory(keyword) {
            // 移除已存在的相同记录
            const index = this.historyList.indexOf(keyword);
            if (index > -1) {
                this.historyList.splice(index, 1);
            }
            // 将新记录添加到最前面
            this.historyList.unshift(keyword);
            // 只保留最近10条记录
            if (this.historyList.length > 10) {
                this.historyList.pop();
            }
            this.saveHistory();
        },
        loadHistory() {
            const history = uni.getStorageSync('search_history');
            if (history) {
                this.historyList = history;
            }
        },
        saveHistory() {
            uni.setStorageSync('search_history', this.historyList);
        },
        clearHistory() {
            uni.showModal({
                title: '提示',
                content: '确定要清除所有搜索记录吗？',
                success: (res) => {
                    if (res.confirm) {
                        this.historyList = [];
                        this.saveHistory();
                    }
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.search-page-container {
    background-color: #fff;
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.custom-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    z-index: 99;
}

.navbar-content {
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    position: relative;
}

.back-icon {
    width: 40rpx;
    height: 40rpx;
}

.search-input-wrapper {
    // flex: 1;
    width: 60%;
    height: 68rpx;
    background-color: #f7f7f7;
    border-radius: 34rpx;
    margin-left: 20rpx;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    border: 1rpx solid #e5e5e5;
}

.search-input {
    flex: 1;
    font-size: 28rpx;
    color: #333;
}

.search-icon {
    width: 32rpx;
    height: 32rpx;
    margin-left: 20rpx;
}

.search-content-area {
    padding: 30rpx;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
}

.section-title {
    font-size: 30rpx;
    font-weight: 500;
    color: #333;
}

.clear-history {
    font-size: 26rpx;
    color: #999;
}

.tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
}

.tag-item {
    background-color: #f7f7f7;
    padding: 10rpx 20rpx;
    border-radius: 8rpx;
    font-size: 26rpx;
    color: #666;
}

.history-section {
    margin-bottom: 50rpx;
}
</style>
