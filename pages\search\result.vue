<template>
  <view class="search-page-container">
    <c-loading2></c-loading2>
    <!-- 自定义导航栏 -->
    <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="navbar-content" :style="{ height: navBarHeight + 'px' }">
        <image class="back-icon" src="/static/common/backBlack.png" @click="goBack"></image>
        <view class="search-input-wrapper">
          <input class="search-input" type="text" v-model="keyword" placeholder="搜索案例"
            placeholder-style="color:#999; font-size: 28rpx;" @confirm="performSearch" />
          <image class="search-icon" src="/static/common/searchBlack.png" @click="performSearch"></image>
        </view>
      </view>
    </view>

    <view class="search-content-area" :style="{ marginTop: (statusBarHeight + navBarHeight) + 'px' }">
      <!-- 搜索结果 -->
      <scroll-view scroll-y class="search-scroll" @scrolltolower="loadMore" @refresherrefresh="onRefresh"
        :refresher-enabled="true" :refresher-triggered="refresherTriggered" enhanced :bounces="false"
        :show-scrollbar="false" v-if="searchResults.length > 0 || isSearching">
        <view class="case-grid">
          <view class="case-card" v-for="(item, itemIndex) in searchResults" :key="itemIndex" hover-class="none"
            @click="goToCaseDetail(item)">
            <view class="case-image-container">
              <image class="case-main-image" :src="vuex_imgUrl + item.coverImage" mode="aspectFill"></image>
              <view class="case-category-tag">{{ item.category }}</view>
            </view>
            <view class="case-info">
              <view class="case-title">{{ item.title }}</view>
              <view class="case-desc">{{ item.subtitle }}</view>
              <view class="case-meta">
                <view class="meta-item">
                  <image src="/static/common/clock.png" />
                  <view class="meta-text">{{ $u.timeFormat(item.completionTime, 'yyyy.mm.dd') }}</view>
                </view>
                <view class="meta-item">
                  <image src="/static/common/ping.png" />
                  <view class="meta-text">{{ item.address }}</view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 加载状态 -->
        <u-loadmore :status="status" v-if="searchResults.length > 0"></u-loadmore>
      </scroll-view>

      <!-- 暂无搜索结果 -->
      <view class="no-data" v-if="searchResults.length === 0 && !isSearching && hasSearched">
        <image src="/static/common/noData.png" class="no-data-image"></image>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      keyword: '',
      searchResults: [], // 搜索结果列表
      pagination: {
        pageNum: 1,
        pageSize: 10
      },
      status: 'loadmore', // 加载状态
      isSearching: false, // 是否正在搜索
      hasSearched: false, // 是否已经搜索过
      refresherTriggered: false, // 下拉刷新状态
      statusBarHeight: 0,
      navBarHeight: 44, // 默认导航栏高度
    };
  },
  onLoad(options) {
    // 显示loading
    this.$u.vuex("vuex_loading2", true);

    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight || 0;

    // 获取搜索关键词
    if (options.keyword) {
      this.keyword = decodeURIComponent(options.keyword);
      this.performSearch(true);
    }

    // 延迟隐藏loading
    this.$nextTick(() => {
      setTimeout(() => {
        this.$u.vuex("vuex_loading2", false);
      }, 1000);
    });
  },
  computed: {
    // 获取图片基础URL
    vuex_imgUrl() {
      return this.$store.state.vuex_imgUrl;
    }
  },
  methods: {
    goBack() {
      uni.navigateBack();
    },

    handleSearch() {
      this.performSearch(true);
    },

    // 执行搜索
    async performSearch(isRefresh = false) {
      if (this.isSearching) return;

      this.isSearching = true;
      this.hasSearched = true;

      if (isRefresh) {
        this.pagination.pageNum = 1;
        this.searchResults = [];
        this.status = 'loadmore';
      }
      try {
        const searchKeyword = this.keyword.trim();
        const params = {
          categoryId: "", // 搜索时不限制分类
          homepageShow: '',
          industry: "",
          keyword: searchKeyword, // 允许空字符串搜索所有案例
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize
        };

        const res = await this.$api.caseList(params);
        const data = res.data;
        const processedList = data.list || [];

        if (isRefresh) {
          this.searchResults = processedList;
        } else {
          this.searchResults = [...this.searchResults, ...processedList];
        }

        if (processedList.length < this.pagination.pageSize) {
          this.status = 'nomore';
        } else {
          this.status = 'loadmore';
          this.pagination.pageNum++;
        }

      } catch (err) {
        console.error("搜索案例失败", err);
        this.status = 'loadmore';
        uni.showToast({
          title: '搜索失败，请重试',
          icon: 'none'
        });
      } finally {
        this.isSearching = false;
      }
    },
    // 加载更多
    loadMore() {
      if (this.status !== 'nomore' && this.status !== 'loading') {
        this.performSearch();
      }
    },
    // 下拉刷新
    onRefresh() {
      this.refresherTriggered = true;
      this.performSearch(true).finally(() => {
        this.refresherTriggered = false;
      });
    },
    // 跳转到案例详情页面
    goToCaseDetail(item) {
      console.log('点击案例详情:', item);
      uni.navigateTo({
        url: `/pages/case/detail?item=${encodeURIComponent(JSON.stringify(item))}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.search-page-container {
  background-color: #fff;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  z-index: 99;
}

.navbar-content {
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  position: relative;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.search-input-wrapper {
  width: 60%;
  height: 68rpx;
  background-color: #f7f7f7;
  border-radius: 34rpx;
  margin-left: 20rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  border: 1rpx solid #e5e5e5;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 20rpx;
}

.search-content-area {
  padding: 30rpx;

  .search-scroll {
    height: calc(100vh - 200rpx);
  }
}

// 卡片样式 - 与case页面保持一致
.case-grid {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  padding: 0 5rpx;

  .case-card {
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    border: 2rpx solid #BCBBB7;

    .case-image-container {
      position: relative;

      .case-main-image {
        width: 100%;
        height: 512rpx;
        display: block;
      }

      .case-category-tag {
        position: absolute;
        top: 24rpx;
        right: 24rpx;
        background: rgba(158, 39, 33, 0.3);
        border-radius: 4rpx;
        font-family: "Regular", Source Han Serif SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #FFFFFF;
        padding: 5rpx 10rpx;
        text-align: center;
      }
    }

    .case-info {
      padding: 42rpx 24rpx 28rpx 24rpx;

      .case-title {
        font-family: "Bold", Source Han Serif SC;
        font-weight: bold;
        font-size: 36rpx;
        color: #111111;
        margin-bottom: 20rpx;
      }

      .case-desc {
        font-family: "Regular", Source Han Serif SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        margin-bottom: 22rpx;
      }

      .case-meta {
        .meta-item {
          display: flex;
          align-items: center;
          margin-bottom: 8rpx;

          &:last-child {
            margin-bottom: 0;
          }

          image {
            width: 24rpx;
            height: 24rpx;
            margin-right: 12rpx;
            margin-top: 3px;
          }

          .meta-text {
            font-family: "Regular", Source Han Serif SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #565656;
          }
        }
      }
    }
  }
}

// 暂无数据样式
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .no-data-image {
    width: 200rpx;
    height: 200rpx;
    margin-bottom: 30rpx;
  }

  .no-data-text {
    font-size: 28rpx;
    color: #999;
  }
}
</style>
