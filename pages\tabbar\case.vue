<template>
  <view class="case-container">
    <c-loading></c-loading>
    <!-- 页面内容 -->
    <view class="page-content">
      <!-- 顶部搜索栏 -->
      <view class="header-section" :style="{ paddingTop: statusHeight + 'px' }">
        <view class="nav-bar" :style="{ height: navHeight + 'px' }">
          <!-- 行业下拉选择 -->
          <view class="industry-selector" hover-class="none" @click="showIndustryPicker">
            <view>{{ selectedIndustryName || '行业' }}</view>
            <image class="down-icon" src="/static/common/down.png" />
          </view>

          <!-- 搜索框 -->
          <view class="search-bar" hover-class="none">
            <image class="search-icon" src="/static/common/searchBlack.png" />
            <input class="search-input" v-model="apiParams.keyword" placeholder="搜索案例" :placeholder-style="{
              fontFamily: 'Regular',
              fontWeight: 400,
              fontSize: '24rpx',
              color: '#666666',
              width: '100%',
              textAlign: center,
              transform: 'translateX(-22.5rpx)',
            }" @input="onSearchInput" @confirm="onSearchConfirm" />
          </view>
        </view>
      </view>

      <!-- 分类标签 -->
      <view class="category-section">
        <scroll-view class="custom-tabs-container" scroll-x :scroll-left="tabsScrollLeft" scroll-with-animation>
          <view v-for="(tab, index) in caseTabs" :key="tab.id" class="custom-tab" :id="'tab-' + index"
            :class="{ active: currentTabIndex === index }" @click="tabChange(index)" hover-class="none">
            {{ tab.name }}
          </view>
        </scroll-view>
        <!-- 更多操作 -->
        <view class="more-actions" hover-class="none">
          <image class="change-list-icon" src="/static/common/changeList.png" @click="toggleListStyle" />
        </view>
      </view>
      <!-- 案例列表 - 滑动切换 -->
      <view class="case-list-section">
        <swiper class="case-swiper" :current="currentTabIndex" @change="onCaseSwiperChange">
          <swiper-item v-for="tab in caseTabs" :key="tab.id">
            <!-- tab加载状态 -->
            <view class="tab-loading" v-if="tabLoadingStates[tab.id]">
              <view class="tab-loading-content">
                <image src="/static/common/loading.png" class="tab-loading-image" />
                <view class="tab-loading-dots">
                  <view class="tab-dot tab-dot1"></view>
                  <view class="tab-dot tab-dot2"></view>
                  <view class="tab-dot tab-dot3"></view>
                </view>
              </view>
            </view>
            <!-- 内容区域 -->
            <scroll-view scroll-y class="main-scroll" @scrolltolower="loadMore" @refresherrefresh="onRefresh"
              :refresher-enabled="true" :refresher-triggered="refresherTriggered" enhanced :bounces="false"
              :show-scrollbar="false" v-else>
              <!-- 卡片样式 -->
              <view class="case-grid" v-if="!isListStyle">
                <view class="case-card" v-for="(item, itemIndex) in getListByTab(tab)" :key="itemIndex"
                  hover-class="none" @click="goToCaseDetail(item)">
                  <view class="case-image-container">
                    <image class="case-main-image" :src="vuex_imgUrl + item.coverImage" mode="aspectFill"></image>
                    <view class="case-category-tag">{{ item.category }}</view>
                  </view>
                  <view class="case-info">
                    <view class="case-title">{{ item.title }}</view>
                    <view class="case-desc">{{ item.subtitle }}</view>
                    <view class="case-meta">
                      <view class="meta-item">
                        <image src="/static/common/clock.png" />
                        <view class="meta-text">{{ $u.timeFormat(item.completionTime, 'yyyy.mm.dd') }}</view>
                      </view>
                      <view class="meta-item">
                        <image src="/static/common/ping.png" />
                        <view class="meta-text">{{ item.address }}</view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
              <!-- 列表样式 -->
              <view class="case-list" v-else>
                <view class="case-list-item" v-for="(item, itemIndex) in getListByTab(tab)" :key="itemIndex"
                  hover-class="none" @click="goToCaseDetail(item)">
                  <image class="case-list-image" :src="vuex_imgUrl + item.coverImage" mode="aspectFill"></image>
                  <view class="case-list-info">
                    <view class="case-list-title">{{ item.title }}</view>
                    <view class="case-list-desc">{{ item.subtitle }}</view>
                    <view class="case-list-date">{{ $u.timeFormat(item.completionTime, 'yyyy.mm.dd') }}</view>
                  </view>
                  <view class="case-list-category">{{ item.category }}</view>
                </view>
              </view>
              <!-- 暂无数据 -->
              <view class="no-data"
                v-if="casesData[tab.id] && casesData[tab.id].list.length === 0 && !tabLoadingStates[tab.id]">
                <image src="/static/common/nodata.png" class="no-data-image" mode="aspectFit"></image>
              </view>
            </scroll-view>
          </swiper-item>
        </swiper>
      </view>
    </view>
    <c-tabbar :selected="3"></c-tabbar>
  </view>
</template>

<script>
export default {
  data() {
    return {
      apiParams: {
        "categoryId": 0,
        "homepageShow": '',
        "industry": "",
        "keyword": "",
      },
      pagination: {
        pageNum: 1,
        pageSize: 10
      },
      status: 'loadmore',
      isDataLoading: false,
      statusHeight: 0,
      navHeight: 44,
      isListStyle: false, // 控制列表样式切换
      currentTabIndex: 0,
      caseTabs: [],
      caseList: [], // 保留原有的caseList用于兼容
      casesData: {}, // 新增：按分类存储案例数据
      tabLoadingStates: {}, // 新增：每个tab的加载状态
      refresherTriggered: false, // 下拉刷新状态
      industry: [],
      selectedIndustryName: '', // 选中的行业名称
      industryPickerRange: [], // 行业选择器数据
      tabsScrollLeft: 0,
      tabsInfo: [],
      tabsContainerWidth: 0,
    };
  },
  onPullDownRefresh() {
    this.getCaseList(true).finally(() => {
      uni.stopPullDownRefresh();
    });
  },
  async onLoad() {
    const systemInfo = uni.getSystemInfoSync();
    this.statusHeight = systemInfo.statusBarHeight || 0;
    this.navHeight = 44;

    // this.$u.vuex("vuex_loading", true);
    await this.getSystemType();
    await this.getCaseCategoryList()
    this.$nextTick(() => {
      setTimeout(() => {
        // this.$u.vuex("vuex_loading", false);
      }, 1000);
    });
  },
  methods: {
    getListByTab(tab) {
      if (!tab || typeof tab.id === 'undefined') {
        return [];
      }
      if (!this.casesData || !this.casesData[tab.id]) {
        return [];
      }
      return this.casesData[tab.id].list || [];
    },
    async getCaseList(isRefresh = false) {
      console.log('getCaseList调用:', {
        isRefresh,
        currentTabIndex: this.currentTabIndex,
        caseTabs: this.caseTabs,
        isDataLoading: this.isDataLoading
      });

      if (this.isDataLoading) return;

      const currentTabId = this.caseTabs[this.currentTabIndex]?.id;
      console.log('当前分类ID:', currentTabId);

      if (currentTabId === undefined) {
        console.log('没有找到当前分类ID，退出');
        return;
      }

      if (isRefresh) {
        this.pagination.pageNum = 1;
        // 初始化当前分类的数据
        if (!this.casesData.hasOwnProperty(currentTabId) || !this.casesData[currentTabId]) {
          this.$set(this.casesData, currentTabId, { list: [], hasMore: true });
        } else {
          this.casesData[currentTabId].list = [];
          this.casesData[currentTabId].hasMore = true;
        }
        this.status = 'loadmore';
      }

      if (this.status === 'nomore') return;

      this.isDataLoading = true;
      this.status = 'loading';
      // 设置当前tab的加载状态
      this.$set(this.tabLoadingStates, currentTabId, true);

      const params = {
        ...this.apiParams,
        ...this.pagination
      };

      console.log('调用API参数:', params);

      try {
        const res = await this.$api.caseList(params);
        console.log('API返回结果:', res);
        const data = res.data;
        const processedList = data.list || [];

        // 存储到对应分类
        if (isRefresh) {
          if (!this.casesData.hasOwnProperty(currentTabId) || !this.casesData[currentTabId]) {
            this.$set(this.casesData, currentTabId, { list: processedList, hasMore: true });
          } else {
            this.casesData[currentTabId].list = processedList;
          }
        } else {
          // 追加到对应分类
          if (this.casesData.hasOwnProperty(currentTabId) && this.casesData[currentTabId]) {
            this.casesData[currentTabId].list = [...this.casesData[currentTabId].list, ...processedList];
          }
        }

        console.log('案例数据更新:', {
          currentTabId,
          dataLength: this.casesData[currentTabId]?.list?.length || 0,
          processedListLength: processedList.length
        });

        if (processedList.length < this.pagination.pageSize) {
          this.status = 'nomore';
          if (this.casesData.hasOwnProperty(currentTabId) && this.casesData[currentTabId]) {
            this.casesData[currentTabId].hasMore = false;
          }
        } else {
          this.status = 'loadmore';
          this.pagination.pageNum++;
        }

      } catch (err) {
        console.error("getCaseList error", err);
        this.status = 'loadmore';
      } finally {
        this.isDataLoading = false;
        // 清除当前tab的加载状态
        setTimeout(() => {
          this.$set(this.tabLoadingStates, currentTabId, false);
        }, 1000)
      }
    },
    loadMore() {
      if (this.status !== 'nomore' && this.status !== 'loading') {
        this.getCaseList();
      }
    },
    // 下拉刷新
    onRefresh() {
      this.refresherTriggered = true;
      this.getCaseList(true).finally(() => {
        this.refresherTriggered = false;
      });
    },
    async getCaseCategoryList() {
      try {
        const res = await this.$api.caseCategoryList();
        res.data.unshift({
          id: "",
          name: "全部"
        });

        this.caseTabs = res.data
        this.apiParams.categoryId = this.caseTabs[0].id;
        console.log("案例分类:", this.caseTabs);
        console.log("设置完caseTabs后，准备调用getCaseList");

        // 设置初始tab的加载状态
        const initialTabId = this.caseTabs[0].id;
        this.$set(this.tabLoadingStates, initialTabId, true);

        // 立即调用getCaseList，不需要nextTick
        this.getCaseList(true);

        this.$nextTick(() => {
          this.getTabsInfo();
        });
      } catch (error) {
        console.log("案例分类:", error);
      }
    },
    async getSystemType() {
      try {
        const res = await this.$api.systemType();
        console.log("行业:", res);
        this.industry = res.data;
        // 处理行业选择器数据
        this.industryPickerRange = this.industry.map(item => item.dictLabel);
        //  {
        //   dictLabel:"xxx",
        // dictValue: '1'
        //  }
      } catch (error) {
        console.log("行业:", error);
      }
    },

    // 切换列表样式
    toggleListStyle() {
      this.isListStyle = !this.isListStyle;
    },
    // 标签切换
    tabChange(index) {
      if (this.currentTabIndex === index) {
        return;
      }
      this.currentTabIndex = index;
      this.apiParams.categoryId = this.caseTabs[index].id;
      // 检查该分类是否已有数据，没有则加载
      const tabId = this.caseTabs[index].id;
      if (!this.casesData.hasOwnProperty(tabId) || !this.casesData[tabId] || this.casesData[tabId].list.length === 0) {
        // 设置加载状态
        this.$set(this.tabLoadingStates, tabId, true);
        this.getCaseList(true);
      }
      this.centerActiveTab(index);
    },
    // swiper切换事件
    onCaseSwiperChange(e) {
      const index = e.detail.current;
      this.currentTabIndex = index;
      this.apiParams.categoryId = this.caseTabs[index].id;
      // 检查该分类是否已有数据，没有则加载
      const tabId = this.caseTabs[index].id;
      if (!this.casesData.hasOwnProperty(tabId) || !this.casesData[tabId] || this.casesData[tabId].list.length === 0) {
        // 设置加载状态
        this.$set(this.tabLoadingStates, tabId, true);
        this.getCaseList(true);
      }
      this.centerActiveTab(index);
    },
    // 跳转到案例详情页面
    goToCaseDetail(item) {
      console.log('点击案例详情:', item);
      uni.navigateTo({
        url: `/pages/case/detail?item=${encodeURIComponent(JSON.stringify(item))}`
      });
    },
    // 搜索输入事件
    onSearchInput() {
      // 如果搜索框为空，自动刷新列表
      if (!this.apiParams.keyword.trim()) {
        const currentTabId = this.caseTabs[this.currentTabIndex]?.id;
        if (currentTabId !== undefined) {
          this.$set(this.tabLoadingStates, currentTabId, true);
        }
        this.getCaseList(true);
      }
    },
    // 搜索确认事件
    onSearchConfirm() {
      // 刷新列表，重新加载数据
      const currentTabId = this.caseTabs[this.currentTabIndex]?.id;
      if (currentTabId !== undefined) {
        this.$set(this.tabLoadingStates, currentTabId, true);
      }
      this.getCaseList(true);
    },
    // 显示行业选择器
    showIndustryPicker() {
      uni.showActionSheet({
        itemList: ['全部', ...this.industryPickerRange],
        success: (res) => {
          if (res.tapIndex === 0) {
            // 选择全部
            this.selectedIndustryName = '全部';
            this.apiParams.industry = '';
          } else {
            // 选择具体行业
            const selectedIndex = res.tapIndex - 1;
            this.selectedIndustryName = this.industryPickerRange[selectedIndex];
            this.apiParams.industry = this.industry[selectedIndex].dictValue;
          }
          // 设置加载状态并刷新列表
          const currentTabId = this.caseTabs[this.currentTabIndex]?.id;
          if (currentTabId !== undefined) {
            this.$set(this.tabLoadingStates, currentTabId, true);
          }
          this.getCaseList(true);
        },
        fail: () => {
          console.log('取消选择行业');
        }
      });
    },
    getTabsInfo() {
      const query = uni.createSelectorQuery().in(this);
      query.select('.custom-tabs-container').boundingClientRect();
      query.selectAll('.custom-tab').boundingClientRect();
      query.exec((rects) => {
        if (!rects[0] || !rects[1] || !rects[1].length) {
          return;
        }
        this.tabsContainerWidth = rects[0].width;
        this.tabsInfo = rects[1];
        this.centerActiveTab(this.currentTabIndex);
      });
    },
    centerActiveTab(index) {
      if (!this.tabsInfo.length || !this.tabsContainerWidth) return;

      const marginRight = uni.upx2px(45);

      let preTabsWidth = 0;
      for (let i = 0; i < index; i++) {
        preTabsWidth += this.tabsInfo[i].width + marginRight;
      }

      const activeTabWidth = this.tabsInfo[index].width;

      let scrollLeft = preTabsWidth - (this.tabsContainerWidth / 2) + (activeTabWidth / 2);

      const totalTabsWidth = this.tabsInfo.reduce((sum, tab) => sum + tab.width, 0) + (this.tabsInfo.length - 1) *
        marginRight;
      const maxScrollLeft = totalTabsWidth - this.tabsContainerWidth;

      if (scrollLeft < 0) {
        scrollLeft = 0;
      } else if (scrollLeft > maxScrollLeft) {
        scrollLeft = maxScrollLeft < 0 ? 0 : maxScrollLeft;
      }

      this.tabsScrollLeft = scrollLeft;
    }
  },
};
</script>

<style lang="scss" scoped>
.case-container {
  height: 100vh;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  padding-bottom: calc(env(safe-area-inset-bottom) + 120rpx);

  .main-scroll {
    width: 100%;
    height: 100%;
  }

  .page-content {
    width: 100%;
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;


    // 顶部搜索栏样式
    .header-section {
      background-color: #fff;
      padding-bottom: 48rpx;

      .nav-bar {
        display: flex;
        align-items: center;
        // justify-content: center;
        padding: 0 32rpx;
        box-sizing: border-box;
        position: relative;

        .industry-selector {
          display: flex;
          align-items: center;
          font-family: "Medium", Source Han Serif SC;
          font-weight: 500;
          font-size: 28rpx;
          color: #000000;
          position: relative;
          left: 0rpx;
          margin-right: 20rpx;

          .down-icon {
            width: 24rpx;
            height: 24rpx;
            margin-left: 6rpx;
          }
        }

        .search-bar {
          display: flex;
          align-items: center;
          width: 320rpx;
          height: 60rpx;
          // background: #f5f5f5;
          border-radius: 32rpx;
          padding: 0 20rpx;
          position: relative;
          border: 2rpx solid #C1C1C1;

          .search-icon {
            width: 46rpx;
            height: 46rpx;
            position: absolute;
            left: 20rpx;
          }

          .search-input {
            font-family: "Regular", Source Han Serif SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #333;
            width: 100%;
            padding-left: 55rpx;
            background: transparent;
            border: none;
            outline: none;
          }

        }
      }
    }

    // /deep/ .search-placeholder {
    //   font-family: "Regular", Source Han Serif SC;
    //   font-weight: 400;
    //   font-size: 24rpx;
    //   color: #666666;
    //   width: 100%;
    //   text-align: center;
    //   transform: translateX(-22.5rpx);
    // }

    // 分类标签样式
    .category-section {
      background-color: #fff;
      padding: 0 32rpx 20rpx;
      position: relative;

      .custom-tabs-container {
        overflow-x: auto;
        white-space: nowrap;
        width: 90%;

        .custom-tab {
          display: inline-block;
          vertical-align: top;
          margin-right: 45rpx;
          position: relative;
          padding-bottom: 10rpx;
          font-family: "Medium", Source Han Serif SC;
          font-weight: 500;
          font-size: 32rpx;
          color: #7b7b7b;
          cursor: pointer;
          transition: color 0.3s;
          white-space: nowrap;
          flex-shrink: 0;
          -webkit-tap-highlight-color: transparent;

          &.active {
            color: #000000;

            &::after {
              content: "";
              position: absolute;
              left: 50%;
              bottom: 0;
              transform: translateX(-50%);
              width: 52rpx;
              height: 6rpx;
              background: #9e2721;
              border-radius: 3rpx;
            }
          }
        }

      }

      .more-actions {
        position: absolute;
        top: 7rpx;
        right: 33rpx;

        .change-list-icon {
          width: 40rpx;
          height: 36rpx;
        }
      }
    }

    // 案例列表区域
    .case-list-section {
      padding: 0 32rpx;
      flex: 1;
      min-height: 0;

      // swiper样式
      .case-swiper {
        height: 100%;

        swiper-item {
          height: 100%;
        }
      }

      // tab加载状态样式
      .tab-loading {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #fff;

        .tab-loading-content {
          display: flex;
          flex-direction: column;
          align-items: center;

          .tab-loading-image {
            width: 156rpx;
            height: 132rpx;
          }

          .tab-loading-dots {
            margin-top: 30rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;

            .tab-dot {
              width: 12rpx;
              height: 12rpx;
              border-radius: 50%;
              background-color: #C8C8C8;
              animation: tabDotPulse 1.2s ease-in-out infinite;

              &.tab-dot1 {
                animation-delay: 0s;
              }

              &.tab-dot2 {
                animation-delay: 0.4s;
              }

              &.tab-dot3 {
                animation-delay: 0.8s;
              }
            }
          }
        }
      }

      .no-data {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .no-data-image {
          width: 156rpx;
          height: 192rpx;
        }
      }

      // 卡片样式
      .case-grid {
        display: flex;
        flex-direction: column;
        gap: 32rpx;
        padding: 0 5rpx;

        .case-card {
          background-color: #fff;
          border-radius: 16rpx;
          overflow: hidden;
          border: 2rpx solid #BCBBB7;

          .case-image-container {
            position: relative;

            .case-main-image {
              width: 100%;
              height: 512rpx;
              display: block;
            }

            .case-category-tag {
              position: absolute;
              top: 24rpx;
              right: 24rpx;
              background: rgba(158, 39, 33, 0.3);
              border-radius: 4rpx;
              font-family: 'Regular', Source Han Serif SC;
              font-weight: 400;
              font-size: 20rpx;
              color: #FFFFFF;
              padding: 5rpx 10rpx;
              text-align: center;
            }
          }

          .case-info {
            padding: 42rpx 24rpx 28rpx 24rpx;

            .case-title {
              font-family: "Bold", Source Han Serif SC;
              font-weight: bold;
              font-size: 36rpx;
              color: #111111;
              margin-bottom: 20rpx;
            }

            .case-desc {
              font-family: "Regular", Source Han Serif SC;
              font-weight: 400;
              font-size: 28rpx;
              color: #666666;
              margin-bottom: 22rpx;
            }

            .case-meta {
              .meta-item {
                display: flex;
                align-items: center;
                margin-bottom: 8rpx;

                &:last-child {
                  margin-bottom: 0;
                }

                image {
                  width: 40rpx;
                  height: 40rpx;
                  margin-right: 12rpx;
                  margin-top: 3px;

                }

                .meta-text {
                  font-family: "Regular", Source Han Serif SC;
                  font-weight: 400;
                  font-size: 24rpx;
                  color: #565656;
                }
              }
            }
          }
        }
      }

      // 列表样式
      .case-list {
        display: flex;
        flex-direction: column;
        gap: 24rpx;
        padding: 0 5rpx;

        .case-list-item {
          display: flex;
          align-items: center;
          background-color: #fff;
          border-radius: 8rpx;
          border: 2rpx solid #BCBBB7;
          position: relative;
          overflow: hidden;

          .case-list-image {
            width: 208rpx;
            height: 208rpx;
            // border-radius: 8rpx;
            margin-right: 24rpx;
            flex-shrink: 0;
          }

          .case-list-info {
            flex: 1;
            height: 208rpx;
            margin-right: 24rpx;

            .case-list-title {
              font-family: "SemiBold", Source Han Serif SC;
              font-weight: 600;
              font-size: 28rpx;
              color: #111111;
              margin-bottom: 12rpx;
              margin-top: 24rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .case-list-desc {
              height: 70rpx;
              font-family: "Regular", Source Han Serif SC;
              font-weight: 400;
              font-size: 24rpx;
              color: #666666;
              margin-bottom: 8rpx;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              line-clamp: 2;
            }

            .case-list-date {
              font-family: "Regular", Source Han Serif SC;
              font-weight: 400;
              font-size: 20rpx;
              color: #999999;
            }
          }

          .case-list-category {
            position: absolute;
            top: 32rpx;
            right: 24rpx;
            background: #F5F5F5;
            border-radius: 4rpx;
            font-family: 'Regular', Source Han Serif SC;
            font-weight: 400;
            font-size: 20rpx;
            color: #9E2721;
            padding: 5rpx 10rpx;
            text-align: center;
          }
        }
      }
    }
  }
}

// tab加载动画
@keyframes tabDotPulse {

  0%,
  100% {
    transform: scale(0.8);
    opacity: 0.7;
  }

  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}
</style>