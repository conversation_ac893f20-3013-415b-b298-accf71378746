<template>
  <view>
    <c-loading></c-loading>
    <view>
      <c-navBar class="topBg" :title="titles" backgroundColor="#fff" isTran isPerch :isBack="true"
        backIcon="/static/common/backBlack.png"></c-navBar>
    </view>
    <view class="scrollWrap">
      <scroll-view scroll-y="true" style="height: 100%">
        <rich-text :nodes="content"></rich-text>
        <!-- <view class="warpTitle">XXXXX平台<text>{{ titles }}</text>协议</view> -->
        <view class="contend"></view>
      </scroll-view>
      <!-- <view class="reBtn1" >保存</view> -->
      <view></view>
    </view>
  </view>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      items: Array(15).fill(null),
      titles: "",
      content: "",
    };
  },
  onLoad(i) {
    if (i.type == 1) {
      this.titles = "用户协议";
      this.getAgreement(1);
    } else if (i.type == 2) {
      this.titles = "隐私协议";
      this.getAgreement(2);
    } else if (i.type == 3) {
      this.titles = "入驻协议";
      this.getAgreement(3);
    } else if (i.type == 4) {
      this.titles = "关于我们";
      this.getAgreement(4);
    } else if (i.type == 7) {
      this.titles = "会员协议";
      this.getAgreement(7);
    } else {
      this.titles = i.title;
      this.content = JSON.parse(i.content);
    }
  },

  methods: {
    getAgreement(id) {
      this.$api
        .agreement({
          id: id,
        })
        .then((res) => {
          this.content = res.data.content;
          console.log(res.data, "2222");
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.scrollWrap {
  height: 88vh;
  padding: 20rpx 20rpx 0 20rpx;
  // 首行缩进
  text-indent: 2em;
  line-height: 1.5;
  font-size: 28rpx;
  color: #333333;
  font-weight: 400;
  font-style: normal;
  text-transform: none;
  margin: auto;

  .warpTitle {
    width: 100vw;
    font-family: PingFang SC, PingFang SC;
    font-weight: bold;
    font-size: 36rpx;
    color: #333333;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin: 40rpx 0 20rpx 0;
  }

  .contend {
    width: 682rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
    font-style: normal;
    text-transform: none;
    text-indent: 2em;
    margin: auto;
  }
}
</style>
