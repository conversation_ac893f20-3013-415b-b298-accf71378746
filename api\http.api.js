const http = uni.$u.http;
const getApi = (params = {}, config) => http.get("/frontend/user/login", { params, ...config });
const getPhone = (params = {}, config) => http.get(`/frontend/user/get/phone`, { params, ...config });
const caseCategoryList = (params = {}, config) => http.get(`/frontend/case/category/list`, { params, ...config });
const caseDetail = (params = {}, config) => http.get(`/frontend/case/detail`, { params, ...config });
const userInfo = (params = {}, config) => http.get(`/frontend/user/info`, { params, ...config });
const systemType = (params = {}, config) => http.get(`/system/dict/data/type/usr_case_industry`, { params, ...config });
const projectList = (params = {}, config) => http.get(`/frontend/project/list`, { params, ...config });
const agreement = (data = {}, config) => http.get('/common/agreement/getById', { params: data, ...config });
const getUserAddressList = (params = {}, config) => http.get("/ruoyi/address/getUserAll", { params, ...config });
const getArea = (params = {}, config) => http.get("/ruoyi/city/getByPid", { params, ...config });
//  ↑ get
//  ↓ post
const postApi = (params = {}, config) => http.post("/frontend/login/account-password", params, config);
const bannerList = (params = {}, config) => http.post("/frontend/banner/list", params, config);
const loginFirst = (params = {}, config) => http.post('/frontend/user/login/first', params, config);
const userLogin = (params = {}, config) => http.post('/frontend/user/login', params, config);
const caseList = (params = {}, config) => http.post(`/frontend/case/list`, params, config);
const userUpdate = (params = {}, config) => http.post('/frontend/user/update', params, config);
const newsList = (params = {}, config) => http.post(`/frontend/community/news/list`, params, config);
const newsDetail = (params = {}, config) => http.post(`/frontend/community/news/detail`, params, config);
const detailComment = (params = {}, config) => http.post(`/frontend/community/news/detail/comment`, params, config);
const commentPublish = (params = {}, config) => http.post(`/frontend/community/news/detail/comment/publish`, params, config);
const newsPublish = (params = {}, config) => http.post(`/frontend/community/news/publish`, params, config);
const newsDetailForward = (params = {}, config) => http.post(`/frontend/community/news/detail/forward`, params, config);
const commonConsultation = (params = {}, config) => http.post(`/frontend/common/consultation`, params, config);
const addAndEditAddress = (data = {}, config) => http.post("/ruoyi/address/insertOrUpdate", data, config);
const delAddress = (params = {}, config) => http.get(`/ruoyi/address/del/${params.id}`, { params: {}, ...config });


let apiList = {
    getApi, // get接口示例
    postApi, // post接口示例
    loginFirst,//是否老用户
    userLogin,//登录
    bannerList,//轮播图片
    getPhone,//获取手机号
    caseCategoryList,//案例分类列表
    caseList,//案例列表
    caseDetail,//案例详情
    userInfo,//用户信息
    systemType,//字典行业分类
    userUpdate,//修改信息
    projectList,//项目
    newsList,//社区列表
    newsDetail,//社区详情
    detailComment,//社区 评论回复树
    commentPublish,//社区发布/回复评论
    newsPublish,//发布社区动态
    newsDetailForward,//点赞及分享
    commonConsultation,//合作咨询填写
    agreement,//协议
    getUserAddressList, // 获取用户收货地址
    addAndEditAddress, // 添加或修改地址
    delAddress, // 删除地址
    getArea, // 获取地区
};
export default { ...apiList };
