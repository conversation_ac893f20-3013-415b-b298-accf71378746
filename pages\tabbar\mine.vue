<template>
    <view class="mine-container">
        <c-loading></c-loading>
        <scroll-view class="page-scroll" scroll-y enhanced :bounces="false" :show-scrollbar="false">
            <!-- 头部 -->
            <view class="header-section" :style="{ paddingTop: statusHeight + 'px' }">
                <view class="nav-bar" :style="{ height: navHeight + 'px' }">
                    <view class="nav-left">
                        <view class="nav-title">我的</view>
                    </view>
                </view>
                <view class="user-info">
                    <view class="avatar-type">
                        <view class="profile-btn" @click="toSetting(true)">
                            <view class="profile-btn-inner">个人资料</view>
                        </view>
                        <view class="avatar-wrapper">
                            <image class="avatar-bg" :src="avatarUrl" />
                        </view>
                        <view class="nav-icon settings-icon" @click="toSetting(false)">
                            <image src="/static/mine/setting.png" class="setting-img" />
                        </view>
                    </view>
                    <view class="username">{{ userInfo.nickname || "登录/注册" }}</view>
                    <view class="phone">{{ maskedPhone }}</view>
                </view>
            </view>

            <!-- 功能卡片 -->
            <view class="features-card">
                <view class="feature-item" @click="toContract">
                    <image class="feature-icon" src="/static/mine/contract.png" mode="aspectFit"></image>
                    <view class="feature-title">我的合同</view>
                    <view class="feature-desc">查看进度</view>
                </view>
                <view class="feature-item" @click="goToProjectProgress">
                    <image class="feature-icon" src="/static/mine/project.png" mode="aspectFit"></image>
                    <view class="feature-title">我的项目</view>
                    <view class="feature-desc">查看进度</view>
                </view>
            </view>

            <!-- 菜单列表 -->
            <view class="menu-list">
                <view class="menu-item" v-for="(item, index) in menuItems" :key="index"
                    @click="$fn.jumpPage(item.path, item.isLogin)">
                    <view class="menu-left">
                        <image class="menu-icon" :src="item.icon" mode="aspectFit"></image>
                        <view class="menu-label">{{ item.label }}</view>
                    </view>
                    <u-icon name="arrow-right" color="#4F4F4F" size="16" bold></u-icon>
                </view>
            </view>
        </scroll-view>
        <view class="version-info">当前版本: 1.01</view>
        <c-tabbar :selected="4"></c-tabbar>
    </view>
</template>

<script>
export default {
    data() {
        return {
            statusHeight: 0,
            navHeight: 0,
            userInfo: {
                avatar: "",
                identity: 0,
                nickname: "",
                phone: "",
            },
            menuItems: [
                { icon: "/static/mine/CouponCenter.png", label: "领券中心", path: "", isLogin: false },
                { icon: "/static/mine/EventZone.png", label: "活动专区", path: "", isLogin: false },
                { icon: "/static/mine/AddressManagement.png", label: "地址管理", path: "/pages/mine/pages/address/index", isLogin: false },
                { icon: "/static/mine/CooperationConsultation.png", label: "合作咨询", path: "/pages/cooperativeConsultation/index", isLogin: false },
                { icon: "/static/mine/AboutUs.png", label: "关于我们", path: "/pages/mine/pages/aboutUs/index", isLogin: false },
                { icon: "/static/mine/ComplaintsandSuggestions.png", label: "投诉建议", path: "/pages/complaint/index", isLogin: false },
                { icon: "/static/mine/MediaMatrix.png", label: "媒体矩阵", path: "", isLogin: false },
            ],
        };
    },
    computed: {
        // 头像完整URL
        avatarUrl() {
            if (this.userInfo.avatar) {
                // 如果是 http 开头的完整 URL，直接使用
                if (this.userInfo.avatar.startsWith("http")) {
                    return this.userInfo.avatar;
                }
                // 否则拼接 vuex_imgUrl
                return this.vuex_imgUrl + this.userInfo.avatar;
            }
            return "";
        },
        // 脱敏手机号
        maskedPhone() {
            if (this.userInfo.phone) {
                // 使用正则表达式隐藏中间4位：13812345678 -> 138****5678
                return this.userInfo.phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
            }
            return "153****0809";
        },
    },

    async onLoad() {
        const systemInfo = uni.getSystemInfoSync();
        this.statusHeight = systemInfo.statusBarHeight || 0;
        const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
        this.navHeight = menuButtonInfo.height + (menuButtonInfo.top - this.statusHeight) * 2;
        this.navHeight = 44;
        this.$u.vuex("vuex_loading", true);
        await this.getUserInfo();
        this.$nextTick(() => {
            setTimeout(() => {
                this.$u.vuex("vuex_loading", false);
            }, 1000);
        });
    },
    methods: {
        toContract() {
            uni.showToast(
                {
                    title: '敬请期待',
                    icon: 'none',
                    duration: 2000
                }
            );
        },
        // 获取用户信息
        async getUserInfo() {
            try {
                const res = await this.$api.userInfo();
                this.userInfo = res.data;
                console.log("用户信息:", res.data);
            } catch (error) {
                console.log("用户信息:", error);
            }
        },
        toSetting(i) {
            uni.navigateTo({
                url: "/pages/mine/pages/settings/index?type=" + i,
                fail(error) {
                    console.log(error);
                },
            });
        },
        // 跳转到项目进度页面
        goToProjectProgress() {
            uni.navigateTo({
                url: "/pages/mine/pages/project/progress",
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.mine-container {
    height: 100vh;
    overflow: hidden;

    .page-scroll {
        height: 100%;
        box-sizing: border-box;
        padding-bottom: calc(env(safe-area-inset-bottom) + 156rpx);
    }
}

.header-section {
    position: relative;
    background-image: url("/static/mine/person.png");
    background-size: cover;
    background-position: center;
    padding-bottom: 120rpx;
    overflow: hidden;

    .nav-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 32rpx;
        position: fixed;
        z-index: 2;

        .nav-left {
            display: flex;
            align-items: center;

            .nav-title {
                font-family: "Bold", Source Han Serif SC;
                font-weight: bold;
                font-size: 44rpx;
                color: #000000;
            }
        }
    }
}

.user-info {
    margin-top: 92rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;

    .avatar-type {
        width: 100%;
        position: relative;
        display: flex;
        justify-content: center;

        .profile-btn {
            width: 128rpx;
            height: 48rpx;
            border-radius: 30rpx;
            position: fixed;
            top: 196rpx;
            left: 32rpx;
            padding: 2rpx;
            background: linear-gradient(135deg, rgba(10, 123, 235, 0.5), rgba(180, 27, 27, 0.5));
            box-sizing: border-box;

            .profile-btn-inner {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 100%;
                height: 100%;
                background-color: #fff;
                border-radius: 28rpx;
                font-family: "Regular", Source Han Serif SC;
                font-weight: 400;
                font-size: 24rpx;
                color: #0b090a;
            }
        }

        .avatar-wrapper {
            width: 140rpx;
            height: 140rpx;
            border: 2rpx solid #adafad;
            border-radius: 50%;

            .avatar-bg {
                width: 100%;
                height: 100%;
                border-radius: 50%;
            }
        }

        .settings-icon {
            width: 40rpx;
            height: 40rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            position: fixed;
            top: 196rpx;
            right: 32rpx;

            .setting-img {
                width: 40rpx;
                height: 40rpx;
            }
        }
    }

    .username {
        font-family: "Bold", Source Han Serif SC;
        font-weight: bold;
        font-size: 36rpx;
        color: #9e2721;
        margin-top: 18rpx;
    }

    .phone {
        font-family: "Regular", Source Han Serif SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #0b090a;
        margin-top: 12rpx;
    }
}

.features-card {
    display: flex;
    justify-content: space-between;
    margin: -96rpx 30rpx 30rpx;
    overflow: hidden;
    gap: 26rpx;

    .feature-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        width: 330rpx;
        height: 250rpx;
        border: 2rpx solid #efe7df;
        border-radius: 24rpx;
        background: #fff;
        z-index: 10;

        .feature-icon {
            width: 76rpx;
            height: 76rpx;
            filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
        }

        .feature-title {
            font-family: "SemiBold", Source Han Serif SC;
            font-weight: 600;
            font-size: 32rpx;
            color: #000000;
        }

        .feature-desc {
            font-family: "SemiBold", Source Han Serif SC;
            font-weight: 600;
            font-size: 24rpx;
            color: #949494;
        }
    }
}

.menu-list {
    background-color: #fff;
    padding: 0 32rpx;
    overflow: hidden;

    .menu-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 27rpx 0;

        &:last-child {
            border-bottom: none;
        }

        .menu-left {
            display: flex;
            align-items: center;

            .menu-icon {
                width: 36rpx;
                height: 36rpx;
                margin-right: 28rpx;
            }

            .menu-label {
                font-family: "SemiBold", Source Han Serif SC;
                font-weight: 600;
                font-size: 28rpx;
                color: #060606;
            }
        }
    }
}

.version-info {
    position: fixed;
    bottom: calc(env(safe-area-inset-bottom) + 132rpx);
    left: 0;
    right: 0;
    font-family: "Regular", Source Han Serif SC;
    font-weight: 400;
    font-size: 20rpx;
    color: #000000;
    width: 100%;
    text-align: center;
    pointer-events: none;
    z-index: 1001;
}
</style>
