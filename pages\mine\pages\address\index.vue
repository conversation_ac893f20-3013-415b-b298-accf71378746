<template>
    <section class="address">
        <c-loading></c-loading>
        <section class="nav-bar">
            <c-navBar title="地址管理" :type="1" isPerch :isBack="true" backIcon="/static/common/backBlack.png"> </c-navBar>
        </section>
        <view class="list-items flex align-center gap-24" v-for="item in list" :key="item.id"
            @click="chooseAddress(item)">
            <view class="address-icon">
                <u-image :lazyLoad="false" :fade="false" src="/static/common/address.png" width="24" height="24"
                    mode="aspectFill"></u-image>
            </view>
            <view class="flex-1 flex justify-between align-center gap-30">
                <view>
                    <view class="tit flex align-center mf-font-32">
                        <view class="name">{{ item.expressName }} {{ item.expressPhone }}</view>
                        <view v-if="item.status == 0" class="tag mf-font-20"> 默认 </view>
                    </view>
                    <view class="adr mf-font-24">{{ item.expressArea }} {{ item.detailedAddress }}</view>
                </view>
                <view class="tools flex justify-end mf-font-24">
                    <text @click.stop="
                        $fn.jumpPage(`/pages/mine/pages/address/createAndEditAddress?type=edit&address=${encodeURIComponent(JSON.stringify(item))}`)
                        ">
                        编辑
                    </text>
                    <text class="del" @click.stop="handleConfirmDelAddress(item.id)">删除</text>
                </view>
            </view>
        </view>

        <view class="footer">
            <view class="btn" @click="$fn.jumpPage(`/pages/mine/pages/address/createAndEditAddress?type=add`, true)">
                +新增收货地址 </view>
        </view>
    </section>
</template>

<script>
export default {
    data() {
        return {
            type: "list",
            list: [],
        };
    },
    onLoad(options) {
        if (options.type) {
            this.type = options.type;
        }
    },
    onShow() {
        this.handleGetUserAddressList();
    },
    methods: {
        // 获取用户收货地址
        async handleGetUserAddressList() {
            try {
                const res = await this.$api.getUserAddressList();
                console.log(res);

                if (res.code === 200) {
                    this.list = res.data;
                    console.log(this.list);
                }
            } catch (err) {
                console.error(err);
                this.$fn.showToast("网络出了一点小问题");
            }
        },

        // 确认删除
        handleConfirmDelAddress(id) {
            uni.showModal({
                title: "提示",
                content: "确定删除该地址吗？",
                success: (res) => {
                    if (res.confirm) {
                        this.handleDelAddress(id);
                    }
                },
            });
        },

        // 删除地址
        async handleDelAddress(id) {
            try {
                const res = await this.$api.delAddress({ id });
                if (res.code === 200) {
                    this.$fn.showToast("删除成功");
                    this.handleGetUserAddressList();
                }
            } catch (err) {
                console.error(err);
                this.$fn.showToast("网络出了一点小问题");
            }
        },
        chooseAddress(item) {
            if (this.type === "choose") {
                const eventChannel = this.getOpenerEventChannel();
                eventChannel.emit('addressSelected', { address: item });
                uni.navigateBack();
            }
        },


    },
};
</script>

<style lang="scss" scoped>
.address {
    .nav-bar {
        font-family: "Bold", Source Han Serif SC;
        font-weight: bold;
        font-size: 36rpx;
        color: #000000;
        flex-shrink: 0;
    }
    .list-items {
        padding: 28rpx 32rpx;
        border-bottom: 1rpx solid #edeff0;

        .tit {
            margin-bottom: 12rpx;
            color: #1a1a1a;

            .name {
                width: 346rpx;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .adr {
            color: #666666;
        }
    }

    .tag {
        margin-left: 32rpx;
        width: 64rpx;
        height: 32rpx;
        background: #fc7701;
        color: #fff;
        text-align: center;
    }

    .tools {
        color: #1183ff;
        width: 150rpx;

        .del {
            margin-left: 24rpx;
            color: #fc3e3e;
        }
    }

    .footer {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 24rpx 0;
        padding-bottom: calc(env(safe-area-inset-bottom) + 24rpx);
        width: 100%;
        display: flex;
        justify-content: center;
        background-color: #fff;
        z-index: 100;

        .btn {
            width: 686rpx;
            height: 88rpx;
            background: #9E2721;
            border-radius: 70rpx;
            color: #ffffff;
            font-size: 32rpx;
            font-weight: bold;
            font-family: "Source Han Serif SC", "Source Han Serif", serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}
</style>
