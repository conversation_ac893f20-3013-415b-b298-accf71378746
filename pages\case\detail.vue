<template>
  <view class="case-detail-container">
    <c-loading2></c-loading2>
    <section class="nav-bar">
      <c-navBar title="案例详情" isPerch :isBack="true" backIcon="/static/common/backBlack.png">
      </c-navBar>
    </section>
    <view v-if="caseData">
      <scroll-view class="page-scroll" scroll-y enhanced :bounces="false" :show-scrollbar="false">

        <!-- 图片轮播 -->
        <view class="swiper-section">
          <swiper v-if="caseData.images && caseData.images.length > 1" class="swiper" circular :indicator-dots="true"
            :autoplay="true" :interval="3000" :duration="500">
            <swiper-item v-for="(img, index) in caseData.images" :key="index">
              <image class="swiper-image" :src="vuex_imgUrl + img" mode="aspectFill"></image>
            </swiper-item>
          </swiper>
          <image v-else-if="caseData.coverImage" class="swiper-image" :src="vuex_imgUrl + caseData.coverImage"
            mode="aspectFill"></image>
          <image v-else class="swiper-image" src="https://picsum.photos/750/750?random=1" mode="aspectFill"></image>
        </view>

        <!-- 案例信息 -->
        <view class="case-info-section">
          <view class="case-title">{{ caseData.title }}</view>
          <view class="case-desc">{{ caseData.subtitle }}</view>
          <view class="case-meta">
            <view class="meta-item">
              <image src="/static/common/clock.png" />
              <view class="meta-text">{{ $u.timeFormat(caseData.completionTime, 'yyyy.mm.dd') }}</view>
            </view>
            <view class="meta-item">
              <image src="/static/common/ping.png" />
              <view class="meta-text">{{ caseData.address }}</view>
            </view>
          </view>
        </view>

        <!-- 富文本内容 -->
        <view class="rich-text-section" v-if="caseData.content">
          <u-parse :content="caseData.content"></u-parse>
        </view>
      </scroll-view>

    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      caseData: null,
    };
  },
  onLoad(options) {
    // 显示loading
    this.$u.vuex("vuex_loading2", true);

    if (options.item) {
      const item = JSON.parse(decodeURIComponent(options.item));
      this.caseData = item;
      // 模拟富文本内容
      if (!this.caseData.content) {
        this.caseData.content = `
						<p>这里是关于<strong>${this.caseData.title}</strong>项目的详细介绍。</p>
						<img src="https://picsum.photos/750/500?random=2" />
						<p>项目在实施过程中，我们遇到了多种挑战，但最终都成功克服。</p>
						<p>这是我临时写的富文本内容的示例，实际内容将从API获取。</p>
					`;
      }
      // 模拟多张图片
      // if (!this.caseData.images || this.caseData.images.length === 0) {
      //   this.caseData.images = [this.caseData.coverImage, 'https://picsum.photos/750/750?random=3', 'https://picsum.photos/750/750?random=4'];
      // }
    }

    // 延迟隐藏loading
    this.$nextTick(() => {
      setTimeout(() => {
        this.$u.vuex("vuex_loading2", false);
      }, 1000);
    });
  },
};
</script>

<style lang="scss" scoped>
.case-detail-container {
  background-color: #fff;
  height: 100vh;

  .nav-bar {
    font-family: 'Bold', Source Han Serif SC;
    font-weight: bold;
    font-size: 36rpx;
    color: #000000;
  }
}

.swiper-section {
  margin-top: 15rpx;
  width: 100%;
  height: 512rpx;

  .swiper {
    width: 100%;
    height: 100%;
  }

  .swiper-image {
    width: 100%;
    height: 100%;
    display: block;
  }
}

.case-info-section {
  padding: 36rpx 24rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .case-title {
    font-family: "Bold", Source Han Serif SC;
    font-weight: bold;
    font-size: 36rpx;
    color: #111111;
    margin-bottom: 16rpx;
  }

  .case-desc {
    font-family: "Regular", Source Han Serif SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
  }

  .case-meta {
    margin-top: 14rpx;

    .meta-item {
      display: flex;
      align-items: center;
      margin-top: 8rpx;

      image {
        width: 40rpx;
        height: 40rpx;
        margin-top: 3px;
      }

      &:last-child {
        margin-top: 0;
      }

      .meta-text {
        margin-left: 12rpx;
        font-family: "Regular", Source Han Serif SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #565656;
      }
    }
  }
}

.page-scroll {
  height: calc(100vh - 200rpx);
  box-sizing: border-box;
}

.rich-text-section {
  padding: 30rpx;
  padding-bottom: 100rpx;
}
</style>
