<template>
  <view class="release-container">
    <c-loading></c-loading>
    <!-- 导航栏 -->
    <section class="nav-bar">
      <c-navBar title="发布" isPerch :isBack="true" backIcon="/static/common/backBlack.png">
      </c-navBar>
    </section>

    <view class="content-scroll">
      <view class="form-warp">
        <!-- 发布内容 -->
        <view class="form-item ">
          <input v-model="postData.title" placeholder="发布标题" class="title-input" maxlength="50"
            :show-confirm-bar="false" placeholder-class="placeholder-style" />
          <textarea v-model="postData.content" placeholder="发布内容" class="content-input" maxlength="500"
            :show-confirm-bar="false" placeholder-class="placeholder-style" />
          <c-upLoadImgs :file.sync="postData.images" :maxCount="3" width="200rpx" height="200rpx" format="Array"
            :isShowDelete="false" @update:file="handleUpload" />
        </view>
        <!-- 位置信息 -->
        <view class="location-section" @click="selectLocation">
          <image src="/static/common/releasePost.png" class="location-icon" />
          <text class="location-text">{{ postData.location || '你在那里' }}</text>
          <image src="/static/common/torightu.png" class="arrow-icon" />
        </view>
      </view>
    </view>
    <!-- 发布按钮 -->
    <view class="publish-warp">
      <view class="publish-btn" @click="publishPost">
        <text class="publish-text">发布</text>
      </view>
    </view>

  </view>
</template>

<script>
export default {
  data() {
    return {
      postData: {
        title: '',
        content: '',
        images: [],
        location: '',
        address: '' // 省市信息
      },
      currentLocation: {
        latitude: '',
        longitude: ''
      }
    };
  },
  onLoad() {
    // 进入页面时获取当前定位
    // this.getCurrentLocation();
  },
  methods: {
    // 获取当前定位
    async getCurrentLocation() {
      try {
        const res = await this.$fn.getLocation();
        this.currentLocation.latitude = res.latitude;
        this.currentLocation.longitude = res.longitude;
        // this.postData.location = '';
      } catch (error) {
        console.log('获取权限', error);
      }

    },

    // 选择位置
    async selectLocation() {
      try {
        // 使用 uni 自带的地点选择
        const getAuthorized = await this.$fn.checkLocationPermission()

        if (!getAuthorized) return

        const res = await this.$fn.chooseLocation();
        console.log('选择的位置:', res);
        // 显示位置名称
        this.postData.location = res.name || res.address;
        // 提取省市信息
        const address = res.address || '';
        const provinceCityMatch = address.match(/^(.+?省)?(.+?市)/);

        if (provinceCityMatch) {
          const province = provinceCityMatch[1] || '';
          const city = provinceCityMatch[2] || '';
          this.postData.address = `${province}${city}`;
        } else {
          // 如果无法匹配省市，使用完整地址
          this.postData.address = address;
        }
        console.log('提取的省市信息:', this.postData.address);

        // 更新当前位置信息
        this.currentLocation.latitude = res.latitude;
        this.currentLocation.longitude = res.longitude;
      } catch (error) {
        console.log('获取地址失败', error);


      }

    },
    handleUpload(fileList) {

      this.postData.images = fileList

    },
    // 发布动态
    async publishPost() {
      // 验证必填项
      if (!this.postData.title.trim()) {
        uni.showToast({
          title: '请输入标题',
          icon: 'none'
        });
        return;
      }

      if (!this.postData.content.trim()) {
        uni.showToast({
          title: '请输入内容',
          icon: 'none'
        });
        return;
      }
      if (!this.postData.address) {
        uni.showToast({
          title: '请获取地址',
          icon: 'none'
        });
        return;
      }

      try {
        // 调用发布接口
        const params = {
          title: this.postData.title.trim(),
          detail: this.postData.content.trim(),
          images: this.postData.images || [],
          address: (this.postData.address || '').replace(/[省市]/g, '') // 省市信息，过滤掉"省"和"市"字
        };

        console.log('发布参数:', params);
        const res = await this.$api.newsPublish(params);
        console.log('发布结果:', res);
        uni.showToast({
          title: '发布成功',
          icon: 'success'
        });

        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);

      } catch (error) {
        console.log('发布失败:', error);
        uni.showToast({
          title: '发布失败，请重试',
          icon: 'none'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.release-container {
  width: 100%;
  height: 100vh;
  background-color: #F7F7F7;
  display: flex;
  flex-direction: column;

  .nav-bar {
    font-family: 'Bold', Source Han Serif SC;
    font-weight: bold;
    font-size: 36rpx;
    color: #000000;
  }

  .content-scroll {
    flex: 1;
    padding-bottom: 20rpx;

    .form-warp {
      padding: 0 32rpx;

      .form-item {
        background-color: #fff;
        border-radius: 24rpx;
        margin: 20rpx 0;
        padding: 24rpx 20rpx;

        .title-input {
          font-family: 'Regular', Source Han Serif SC;
          font-weight: 500;
          font-size: 28rpx;
          color: #333;
          min-height: 80rpx;
          background: #F7F7F7;
          margin-bottom: 24rpx;
          padding-left: 20rpx;
          border-radius: 12rpx;
        }

        .content-input {
          width: calc(100% - 40rpx);
          font-family: 'Regular', Source Han Serif SC;
          font-weight: 400;
          font-size: 28rpx;
          color: #333;
          line-height: 1.6;
          min-height: 332rpx;
          background: #F7F7F7;
          margin-bottom: 24rpx;
          padding: 20rpx;
          border-radius: 12rpx;
        }
      }

      .location-section {
        display: flex;
        align-items: center;
        padding: 30rpx 32rpx;
        margin: 24rpx 0;
        background: #FFFFFF;
        border-radius: 24rpx;

        .location-icon {
          width: 40rpx;
          height: 40rpx;
          margin-right: 8rpx;
        }

        .location-text {
          flex: 1;
          font-family: 'Medium', Source Han Serif SC;
          font-weight: 500;
          font-size: 28rpx;
          color: #000000;
        }

        .arrow-icon {
          width: 40rpx;
          height: 40rpx;
        }
      }
    }
  }

  .publish-warp {
    padding: 32rpx 32rpx;
    padding-bottom: calc(32rpx + env(safe-area-inset-bottom));

    .publish-btn {
      background: #9E2721;
      border-radius: 50rpx;
      height: 100rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      &:active {
        transform: scale(0.98);
      }

      .publish-text {
        font-family: 'SemiBold', Source Han Serif SC;
        font-weight: 600;
        font-size: 32rpx;
        color: #fff;
      }
    }
  }
}

/deep/ .placeholder-style {
  font-family: 'Regular', Source Han Serif SC;
  font-weight: 400;
  font-size: 28rpx;
  color: rgba(153, 153, 153, 0.6);
}
</style>
