<template>
  <view class="home-container">
    <c-loading></c-loading>
    <view class="fixed-search-header" :class="scrollTop >= 500 ? 'scrolled' : ''"
      :style="{ paddingTop: statusHeight + 'px' }">
      <view class="nav-bar" :style="{ height: navHeight + 'px' }">
        <view class="search-bar" @click="goToSearchPage">
          <image class="search-icon" src="/static/common/search.png" />
          <view class="search-placeholder">搜索案例</view>
        </view>
      </view>
    </view>
    <scroll-view class="main-scroll" scroll-y="true" @scroll="onScroll" enhanced :bounces="false"
      :show-scrollbar="false">
      <!-- 页面内容 -->
      <view class="page-content">
        <!-- 轮播图 -->
        <view class="swiper-section">
          <!-- <view class="header-on-swiper" :style="{ paddingTop: statusHeight + 'px' }">
            <view class="nav-bar" :style="{ height: navHeight + 'px' }">
              <view class="search-bar" @click="goToCasePage">
                <image class="search-icon" src="/static/common/search.png" />
                <view class="search-placeholder">搜索案例</view>
              </view>
            </view>
          </view> -->
          <view class="swiper" v-if="swiperList.length == 1">
            <image class="swiper-image" :src="vuex_imgUrl + swiperList[0].image" width='100%' height='1462rpx' />
          </view>
          <view class="swiper" v-else>
            <swiper class="mySwiper" circular :indicator-dots="false" :autoplay="true" :interval="3000" :duration="500"
              @change="swiperChange">
              <swiper-item v-for="(item, index) in swiperList" :key="index">
                <image class="swiper-image" :src="vuex_imgUrl + item.image" width='100%' height='1462rpx' />
              </swiper-item>
            </swiper>
          </view>
          <!-- Custom Dots -->
          <view class="custom-indicator" v-if="swiperList.length > 1">
            <view v-for="(item, index) in swiperList" :key="index" class="custom-dot"
              :class="{ active: currentSwiperIndex === index }"></view>
          </view>
        </view>
        <!-- 服务项目 -->
        <view class="service-section section-block"
          style="padding: 40rpx 32rpx;padding-bottom: 70rpx; margin-bottom: 10rpx;">
          <view class="section-header">
            <view>
              <view class="section-title">
                <view>提供服务</view>
                <view style="font-size: 28rpx;"></view>
              </view>
              <view class="section-subtitle">汇聚木风动态、行业资讯等</view>
            </view>
            <view class="section-more" style="margin-bottom: 0rpx;" @click="goToPage('/pages/provideService/index', 1)">
              <view style="margin-right: 4rpx">更多</view>
              <image src="/static/common/toMore.png" style="width: 26rpx; height: 26rpx; margin-top: 2rpx" />
            </view>
          </view>
          <view class="service-swiper-container">
            <swiper class="service-swiper" circular :autoplay="false" :interval="5000" :duration="500"
              @change="serviceSwiperChange">
              <swiper-item>
                <view class="service-grid">
                  <view class="service-item big-item" @click="goToPage('/pages/provideService/index', 1)">
                    <image class="service-bg-image"
                      src="https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>"
                      mode="aspectFill">
                    </image>
                  </view>
                  <view class="service-item bg-item" @click="goToPage('/pages/provideService/index', 1)">
                    <image class="service-image"
                      src="https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>"
                      mode="aspectFill">
                    </image>
                  </view>
                  <view class="service-item" @click="goToPage('/pages/provideService/index', 1)">
                    <image class="service-image"
                      src="https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>"
                      mode="aspectFill">
                    </image>
                  </view>
                </view>
                <!-- /pages/mufengSoftware/index -->
                <view class="solution-banner" @click="goToPage('')">
                  <view style="padding: 0 5rpx;">
                    <image class="banner-image"
                      src="https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>"
                      mode="aspectFill"></image>
                  </view>
                </view>
              </swiper-item>
              <swiper-item>
                <view class="service-grid">
                  <!-- /pages/mufengDesign/index -->
                  <view class="service-item big-item" @click="goToPage('/pages/provideService/index', 2)">
                    <image class="service-bg-image"
                      src="https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>"
                      mode="aspectFill">
                    </image>
                  </view>
                  <view class="service-item bg-item" @click="goToPage('/pages/provideService/index', 2)">
                    <image class="service-image"
                      src="https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>"
                      mode="aspectFill">
                    </image>
                  </view>
                  <view class="service-item" @click="goToPage('/pages/provideService/index', 2)">
                    <image class="service-image"
                      src="https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>"
                      mode="aspectFill">
                    </image>
                  </view>
                </view>
                <view class="solution-banner" @click="goToPage('')">
                  <view style="padding: 0 5rpx;">
                    <image class="banner-image"
                      src="https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>"
                      mode="aspectFill"></image>
                  </view>
                </view>
              </swiper-item>
            </swiper>
            <view class="swiper-indicator-container" style="bottom: -50rpx;">
              <view v-for="(item, index) in 2" :key="index" class="custom-dot"
                :class="{ active: currentServiceSwiperIndex === index }"></view>
            </view>
          </view>
        </view>

        <!-- 全行业案例 -->
        <view class="cases-section section-block" style="margin-top: 0; padding: 0 32rpx">
          <view class="section-header">
            <view>
              <view class="section-title">行业案例</view>
              <view class="section-subtitle">汇聚木风动态、行业资讯等</view>
            </view>
            <view class="section-more" @click="goToCasePage">
              <view>更多</view>
              <image src="/static/common/toMore.png" style="width: 26rpx; height: 26rpx; margin-top: 2rpx" />
            </view>
          </view>
          <scroll-view class="custom-tabs-container" scroll-x :scroll-left="tabsScrollLeft" scroll-with-animation>
            <view v-for="(tab, index) in caseTabs" :key="tab.id" class="custom-tab" :id="'tab-' + index"
              :class="{ active: currentTabIndex === index }" @click="tabChange(index)">
              {{ tab.name }}
            </view>
          </scroll-view>
          <swiper class="case-swiper" :current="currentTabIndex" @change="onCaseSwiperChange"
            :style="{ height: swiperHeight + 'px' }">
            <swiper-item v-for="(tab, index) in caseTabs" :key="tab.id">
              <view class="case-list-wrapper">
                <view class="case-list" :id="`case-list-${index}`">
                  <template v-if="casesData[index] && casesData[index].list.length > 0">
                    <view class="case-card" v-for="i in casesData[index].list" :key="i.id"
                      @click="goToCaseDetail(i)">
                      <image class="case-main-image" :src="vuex_imgUrl + i.coverImage" mode="aspectFill"></image>
                      <view class="case-info">
                        <view class="case-title">{{ i.title }}</view>
                        <view class="case-desc">{{ i.subtitle }}</view>
                        <view class="case-meta">
                          <view class="meta-item">
                            <image src="/static/common/clock.png" />
                            <view class="meta-text">{{ $u.timeFormat(i.completionTime, 'yyy.mm.dd') }}</view>
                          </view>
                          <view class="meta-item">
                            <image src="/static/common/ping.png" />
                            <view class="meta-text">{{ i.address }}</view>
                          </view>
                        </view>
                      </view>
                    </view>
                  </template>
                  <!-- <view v-else class="empty-case-list">
                    暂无相关案例
                  </view> -->
                </view>
              </view>
            </swiper-item>
          </swiper>
        </view>

        <!-- 数字化解决方案 -->
        <view class="solution-section section-block">
          <view class="section-header">
            <view>
              <view class="section-title">解决方案</view>
              <view class="section-subtitle">汇聚木风动态、行业资讯等</view>
            </view>
            <view class="section-more"> </view>
          </view>
          <view class="solution-swiper-container">
            <swiper class="solution-swiper" circular :autoplay="true" :interval="3000" :duration="500"
              @change="solutionSwiperChange">
              <swiper-item v-for="(item, index) in solutionItems" :key="index" class="solution-item">
                <view class="solution-image-container">
                  <image class="solution-image1" :src="item.image1" mode="aspectFill" />
                  <image class="solution-image2" :src="item.image2" mode="aspectFill" />
                </view>
              </swiper-item>
            </swiper>
            <view class="swiper-indicator-container">
              <view v-for="(item, index) in solutionItems" :key="index" class="custom-dot"
                :class="{ active: currentSolutionSwiperIndex === index }"></view>
            </view>
          </view>
        </view>

        <!-- 品质保证 -->
        <view class="guarantee-section section-block">
          <view class="section-header">
            <view>
              <view class="section-title">品质保证</view>
              <view class="section-subtitle">汇聚木风动态、行业资讯等</view>
            </view>
            <view class="section-more"></view>
          </view>
          <view class="guarantee-grid">
            <view class="guarantee-item" v-for="(item, index) in guaranteeItems" :key="index">
              <image class="guarantee-image" :src="item.image" mode="aspectFill"></image>
              <!-- <view class="guarantee-title">{{ item.title }}</view> -->
            </view>
          </view>
        </view>

        <!-- 合作伙伴 -->
        <view class="partners-section section-block" style="padding-bottom: 0;">
          <view class="section-header">
            <view>
              <view class="section-title">合作伙伴</view>
              <view class="section-subtitle">政企伙伴1000+，丰富的项目经验</view>
            </view>
            <view class="section-more"></view>
          </view>
          <view class="sphere-container">
            <view class="sphere">
              <view class="sphere-item-placer" v-for="(item, index) in partnersLogos" :key="index" :style="item.style">
                <view class="sphere-item">
                  <image :src="item.url" mode="aspectFit"></image>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 木风视界 -->
        <view class="vision-section section-block">
          <view class="section-header">
            <view>
              <view class="section-title">木风视界</view>
              <view class="section-subtitle">MUFENG VIDEO</view>
            </view>
            <view class="section-more"></view>
          </view>
          <view class="vision-video">
            <video class="vision-video-player"
              src="https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/4c81fac31ac26027d06affdcaf81690c.mp4"
              controls></video>
            <!-- poster -->
          </view>
          <view class="vision-desc">我司承接“成都交子金控集团项目”上线发布会，多家电视媒体记者现场采访我司总经理-吴丹枫</view>
        </view>
      </view>
    </scroll-view>
    <c-tabbar :selected="0"></c-tabbar>
  </view>
</template>

<script>

export default {
  data() {
    return {
      currentSwiperIndex: 0,
      currentServiceSwiperIndex: 0,
      swiperList: [],
      statusHeight: 0,
      navHeight: 0,
      scrollTop: 0, // 滚动距离
      caseTabs: [],
      solutionItems: [
        {
          image1: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>",
          image2: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>",
        },
        {
          image1: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>",
          image2: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>",
        }, {
          image1: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>",
          image2: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>",
        },
      ],
      guaranteeItems: [
        {
          title: "技术保障",
          image: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>",
        },
        {
          title: "流程保障",
          image: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>",
        },
        {
          title: "标准化",
          image: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>",
        },
      ],
      partnersLogos: [
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/Slice <EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/Slice <EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/Slice <EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/Slice <EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/Slice <EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/Slice <EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/Slice <EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/Slice <EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/Slice <EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/Slice <EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/Slice <EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/Slice <EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>" },
        { url: "https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>" },

      ],
      currentTabIndex: 0,
      currentSolutionSwiperIndex: 0,
      casesData: [],
      swiperHeight: 0,
      tabsScrollLeft: 0,
      tabsInfo: [],
      tabsContainerWidth: 0,
    };
  },

  onReady() {
    this.initSphereLogos();
  },

  async onLoad() {
    const systemInfo = uni.getSystemInfoSync();
    this.statusHeight = systemInfo.statusBarHeight || 0;
    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    this.navHeight =
      menuButtonInfo.height + (menuButtonInfo.top - this.statusHeight) * 2;
    this.navHeight = 44;
    this.$u.vuex("vuex_loading", true);
    await this.infoBannerList();
    await this.getCaseCategoryList()
    this.$nextTick(() => {
      setTimeout(() => {
        this.$u.vuex("vuex_loading", false);
      }, 1000);
    });

  },
  methods: {
    onScroll(e) {
      this.scrollTop = e.detail.scrollTop;
    },
    initSphereLogos() {
      const logos = this.partnersLogos;
      const totalLogos = logos.length;
      if (totalLogos === 0) return;

      const radius = 150; // 半径
      const updatedLogos = [];

      // Define rings: 3 rows, 10 icons per row
      const ringConfigs = [
        { y: 40, count: 7 },  // Top ring
        { y: 0, count: 7 },   // Middle ring
        { y: -40, count: 7 }  // Bottom ring
      ];

      let logoIndex = 0;

      ringConfigs.forEach((config, ringIndex) => {
        const y = config.y;
        const iconsInRing = config.count;
        if (logoIndex >= totalLogos) return;

        // Calculate the radius of the ring in the XZ plane
        const ringRadius = Math.sqrt(radius * radius - y * y);

        for (let i = 0; i < iconsInRing; i++) {
          if (logoIndex >= totalLogos) break;

          // Stagger the middle ring for better appearance
          const angleOffset = (ringIndex % 2 !== 0) ? (Math.PI / iconsInRing) : 0;
          const angle = (2 * Math.PI / iconsInRing) * i + angleOffset;

          const x = ringRadius * Math.cos(angle);
          const z = ringRadius * Math.sin(angle);

          const style = `transform: translate3d(${x}px, ${y}px, ${z}px);`;

          updatedLogos.push({
            ...logos[logoIndex],
            style
          });
          logoIndex++;
        }
      });

      this.partnersLogos = updatedLogos;
    },
    // 视频加载错误处理
    onVideoError(e) {
      console.log("视频加载失败:", e);
      // 可以在这里显示错误提示或使用备用视频
    },
    async infoBannerList() {
      try {
        const res = await this.$api.bannerList();
        this.swiperList = res.data;

        console.log("轮播图:", this.swiperList);
      } catch (error) { console.log("轮播图:", error); }
    },
    async getCaseCategoryList() {
      try {
        const res = await this.$api.caseCategoryList();
        res.data.unshift({
          id: "",
          name: "全部"
        });
        this.caseTabs = res.data;
        this.casesData = this.caseTabs.map(() => ({ list: [], loaded: false, height: 0 }));
        this.currentTabIndex = 0;
        await this.infoCaseList(this.currentTabIndex);
        this.$nextTick(() => {
          this.getTabsInfo();
        });
        console.log("案例分类:", this.caseTabs);
      } catch (error) { console.log("案例分类:", error); }
    },
    async infoCaseList(index) {
      if (this.casesData[index] && this.casesData[index].loaded) {
        if (this.casesData[index].height > 0) {
          this.swiperHeight = this.casesData[index].height;
        } else {
          this.$nextTick(() => {
            this.updateSwiperHeight(index);
          });
        }
        return;
      }

      const categoryId = this.caseTabs[index].id;
      try {
        const res = await this.$api.caseList(
          {
            "categoryId": categoryId,
            "homepageShow": true,
          }
        );
        this.$set(this.casesData, index, {
          list: res.data.list.slice(0, 1),
          loaded: true,
          height: 0
        });

        this.$nextTick(() => {
          this.updateSwiperHeight(index);
        });
      } catch (error) { console.log("案例列表:", error); }
    },

    updateSwiperHeight(index) {
      const query = uni.createSelectorQuery().in(this);
      query.select(`#case-list-${index}`).boundingClientRect(data => {
        if (data && data.height) {
          this.swiperHeight = data.height;
          if (this.casesData[index]) {
            this.casesData[index].height = data.height;
          }
        } else {
          this.swiperHeight = 0;
        }
      }).exec();
    },

    swiperChange(e) {
      this.currentSwiperIndex = e.detail.current;
    },
    serviceSwiperChange(e) {
      this.currentServiceSwiperIndex = e.detail.current;
    },
    solutionSwiperChange(e) {
      this.currentSolutionSwiperIndex = e.detail.current;
    },
    tabChange(index) {
      if (this.currentTabIndex === index) {
        return;
      }
      this.currentTabIndex = index;
      this.centerActiveTab(index);
    },
    onCaseSwiperChange(e) {
      const newIndex = e.detail.current;
      this.currentTabIndex = newIndex;
      this.infoCaseList(newIndex);
      this.centerActiveTab(newIndex);
    },
    // 跳转到搜索页面
    goToSearchPage() {
      uni.navigateTo({
        url: "/pages/search/index",
      });
    },
    // 跳转到案例库页面
    goToCasePage() {
      uni.switchTab({
        url: "/pages/tabbar/case",
      });
    },
    goToCaseDetail(item) {
      uni.navigateTo({
        url: `/pages/case/detail?item=${encodeURIComponent(JSON.stringify(item))}`
      });
    },
    goToPage(url, i) {
      if (url) {
        // 如果有参数i，则在url后面添加参数
        let finalUrl = url;
        if (i !== undefined && i !== null) {
          finalUrl = url.includes('?') ? `${url}&i=${i}` : `${url}?i=${i}`;
        }
        uni.navigateTo({
          url: finalUrl
        });
      } else {
        uni.showToast({
          title: '敬请期待',
          icon: 'none'
        })
      }

    },
    getTabsInfo() {
      const query = uni.createSelectorQuery().in(this);
      query.select('.custom-tabs-container').boundingClientRect();
      query.selectAll('.custom-tab').boundingClientRect();
      query.exec((rects) => {
        if (!rects[0] || !rects[1] || !rects[1].length) {
          return;
        }
        this.tabsContainerWidth = rects[0].width;
        this.tabsInfo = rects[1];
        this.centerActiveTab(this.currentTabIndex);
      });
    },
    centerActiveTab(index) {
      if (!this.tabsInfo.length || !this.tabsContainerWidth) return;

      const marginRight = uni.upx2px(45);

      let preTabsWidth = 0;
      for (let i = 0; i < index; i++) {
        preTabsWidth += this.tabsInfo[i].width + marginRight;
      }

      const activeTabWidth = this.tabsInfo[index].width;

      let scrollLeft = preTabsWidth - (this.tabsContainerWidth / 2) + (activeTabWidth / 2);

      const totalTabsWidth = this.tabsInfo.reduce((sum, tab) => sum + tab.width, 0) + (this.tabsInfo.length - 1) *
        marginRight;
      const maxScrollLeft = totalTabsWidth - this.tabsContainerWidth;

      if (scrollLeft < 0) {
        scrollLeft = 0;
      } else if (scrollLeft > maxScrollLeft) {
        scrollLeft = maxScrollLeft < 0 ? 0 : maxScrollLeft;
      }

      this.tabsScrollLeft = scrollLeft;
    }
  },
};
</script>

<style lang="scss" scoped>
.home-container {
  height: 100vh;
  width: 100%;
  background-color: #fff;
  overflow: hidden;

  // 固定搜索框
  .fixed-search-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    transition: all 0.3s ease;

    // 滚动后的样式
    &.scrolled {
      // background-color: rgba(255, 255, 255, 0.75);
      background: #fff;
      backdrop-filter: blur(10rpx);
      padding: 20rpx 0;
      // border-radius: 0 0 16rpx 16rpx;
      // box-shadow: rgba(0, 0, 0, 0.16) 0px 3px 6px, rgba(0, 0, 0, 0.23) 0px 3px 6px;

      .search-bar {
        background-color: rgba(0, 0, 0, 0.1); // 浅灰色背景

        .search-icon {
          filter: brightness(0.2); // 图标变黑
        }

        .search-placeholder {
          color: #333; // 文字变黑色
        }
      }
    }
  }

  .main-scroll {
    height: 100%;
  }

  // 导航栏样式
  .nav-bar {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    width: 100%;
    padding: 40rpx;
    border-radius: 0 0 16rpx 16rpx;

    .search-bar {
      display: flex;
      align-items: center;
      width: 500rpx;
      height: 64rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 46rpx;
      position: relative;
      justify-content: center;
      transition: all 0.3s ease;

      .search-icon {
        position: absolute;
        width: 46rpx;
        height: 46rpx;
        left: 22rpx;
        top: 50%;
        transform: translateY(-50%);
        transition: all 0.3s ease;
      }

      .search-placeholder {
        font-family: "Regular", Source Han Serif SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #ffffff;
        text-align: center;
        transition: all 0.3s ease;
      }
    }
  }

  // 轮播图区域
  .swiper-section {
    position: relative;
    width: 100%;
    height: 1462rpx;

    .header-on-swiper {
      position: absolute;
      top: -7.3rpx;
      left: 0;
      width: 100%;
      z-index: 10;
      background: transparent;

      // .nav-bar {
      //   display: flex;
      //   align-items: center;
      //   justify-content: space-between;
      //   box-sizing: border-box;
      //   width: 500rpx;
      //   margin-left: 40rpx;
      //   z-index: 10;

      //   .search-bar {
      //     display: flex;
      //     align-items: center;
      //     flex-grow: 1;
      //     height: 64rpx;
      //     background: rgba(255, 255, 255, 0.2);
      //     border-radius: 46rpx;
      //     position: relative;
      //     justify-content: center;

      //     .search-icon {
      //       position: absolute;
      //       width: 46rpx;
      //       height: 46rpx;
      //       left: 22rpx;
      //       top: 50%;
      //       transform: translateY(-50%);
      //     }

      //     .search-placeholder {
      //       font-family: "Regular", Source Han Serif SC;
      //       font-weight: 400;
      //       font-size: 24rpx;
      //       color: #ffffff;
      //       text-align: center;
      //     }
      //   }

      //   .header-icons {
      //     display: flex;
      //     align-items: center;
      //     margin-left: 30rpx;

      //     u-icon:last-child {
      //       margin-left: 24rpx;
      //     }
      //   }
      // }
    }

    .custom-indicator {
      position: absolute;
      bottom: 408rpx;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      z-index: 5;

      .custom-dot {
        width: 12rpx;
        height: 12rpx;
        border-radius: 50%;
        background-color: #b8b8b8;
        margin: 0 8rpx;
        transition: background-color 0.3s;

        &.active {
          background-color: #9e2721;
        }
      }
    }

    .swiper {
      width: 100%;
      height: 100%;

      ::v-deep .uni-swiper-dots {
        bottom: 408rpx;
      }

      ::v-deep .uni-swiper-dot {
        width: 12rpx;
        height: 12rpx;
      }

      .mySwiper {
        width: 100%;
        height: 100%;
      }

      .swiper-image {
        width: 100%;
        height: 100%;
      }
    }
  }

  // 页面内容区域
  .page-content {
    width: 100%;
    padding-bottom: calc(env(safe-area-inset-bottom) + 120rpx);

    // 推广内容
    .promo-content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: #fff;
      padding: 0 50rpx;

      .promo-title {
        font-size: 48rpx;
        font-weight: bold;
      }

      .promo-subtitle {
        font-size: 30rpx;
        margin-top: 20rpx;
      }

      .promo-button {
        margin-top: 40rpx;
        border: 1rpx solid #fff;
        background-color: transparent;
        color: #fff;
        border-radius: 8rpx;
        font-size: 28rpx;
        padding: 10rpx 40rpx;
      }
    }

    // 通用区块样式
    .section-block {
      background-color: #fff;
      border-radius: 20rpx;
      padding: 10rpx 32rpx;
      position: relative;
      z-index: 2;

      .shadow {
        text-shadow: none !important;
      }
    }

    // 各个区块样式
    .solution-section,
    .guarantee-section,
    .partners-section,
    .vision-section {
      margin-top: 30rpx;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: nowrap;
    }

    .section-title {
      font-family: "Bold", Source Han Serif SC;
      font-weight: bold;
      font-size: 36rpx;
      color: #000000;
      display: flex;
      align-items: flex-end;
    }

    .section-more {
      display: flex;
      align-items: center;
      font-family: "SemiBold", Source Han Serif SC;
      font-weight: 600;
      font-size: 24rpx;
      color: #0840ad;
    }

    .section-subtitle {
      font-family: "Regular", Source Han Serif SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #666666;
      margin-top: 8rpx;
    }

    .service-swiper-container {
      position: relative;
      // padding-bottom: 40rpx;
      /* space for indicator */
    }

    .service-swiper {
      height: 736rpx;
    }

    .swiper-indicator-container {
      position: absolute;
      bottom: -24rpx;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      z-index: 5;

      .custom-dot {
        width: 12rpx;
        height: 12rpx;
        border-radius: 50%;
        background-color: #b8b8b8;
        margin: 0 8rpx;
        transition: background-color 0.3s;

        &.active {
          background-color: #9e2721;
        }
      }
    }

    .swiper-item-wrapper {
      border-radius: 16rpx;
      overflow: hidden;
      height: 100%;
    }

    .service-indicator-container {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      z-index: 5;

      .custom-dot {
        width: 12rpx;
        height: 12rpx;
        border-radius: 50%;
        background-color: #b8b8b8;
        margin: 0 8rpx;
        transition: background-color 0.3s;

        &.active {
          background-color: #9e2721;
        }
      }
    }

    // 服务网格
    .service-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 19rpx;
      margin-top: 24rpx;
      padding: 0 5rpx;
    }

    .service-item {
      // background-color: #f5f5f5;
      border-radius: 10rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 268rpx;
      position: relative;

      &.big-item {
        grid-row: span 2;
        height: 555rpx;
        padding: 0;
        overflow: hidden;
        display: block;
      }
    }

    .bg-item {
      background: transparent;
    }

    .service-bg-image {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      z-index: 1;
    }

    .service-info {
      .service-title {
        font-family: "Bold", Source Han Serif SC;
        font-weight: bold;
        font-size: 32rpx;
        color: #fff;
        // text-shadow: 0px 4rpx 4rpx #bdcae3;
        margin: 24rpx 0 0 24rpx;
        position: relative;
        z-index: 20;

      }

      .service-desc {
        font-family: "Regular", Source Han Serif SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #fff;
        margin-top: 3rpx;
        margin-left: 24rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        position: relative;
        z-index: 1;
      }
    }

    .big-item .service-info {
      position: absolute;
      bottom: 36rpx;
      left: 24rpx;
      z-index: 2;

      .service-title {
        font-family: "Bold", Source Han Serif SC;
        font-weight: bold;
        font-size: 32rpx;
        color: #fff;
        // text-shadow: 0px 4rpx 4rpx #bdcae3;
        margin: 0;
        margin-bottom: 4rpx;
      }

      .service-desc {
        font-family: "Regular", Source Han Serif SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #fff;
        margin: 0;
      }
    }

    .service-image {
      width: 100%;
      height: 100%;
      position: absolute;
      right: 0;
      bottom: 0;
    }

    .big-item .service-image {
      height: 320rpx;
    }

    .stats-overlay {
      position: absolute;
      top: 40rpx;
      left: 24rpx;
      z-index: 2;
    }

    .stat-tag {
      background-color: #f56c6c;
      color: #fff;
      font-size: 20rpx;
      padding: 4rpx 12rpx;
      border-radius: 6rpx;
    }

    .stat-bar {
      width: 44rpx;
      height: 8rpx;
      background: #0840ad;
    }

    .solution-banner {
      position: relative;
      margin-top: 19rpx;
      width: 100%;
      // padding: 0 8rpx;
      // border-radius: 16rpx;

      .banner-image {
        width: 100%;
        height: 128rpx;
        display: block;

      }

      .banner-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-family: "Bold", Source Han Serif SC;
        font-weight: bold;
        font-size: 32rpx;
        color: #fff;
        // text-shadow: 0px 4rpx 4rpx #bdcae3;
      }
    }

    .case-list {
      margin-top: 24rpx;
      padding: 0 5rpx;
      padding-bottom: 1rpx;

    }

    .case-card {
      background-color: #fff;
      border-radius: 12rpx;
      margin-bottom: 30rpx;
      overflow: hidden;
      border: 2rpx solid #bcbbb7;
    }

    .empty-case-list {
      text-align: center;
      padding: 100rpx 0;
      color: #999;
      font-size: 28rpx;
    }

    .case-main-image {
      width: 100%;
      height: 512rpx;
      display: block;
    }

    .case-info {
      padding: 36rpx 24rpx;

      .case-title {
        font-family: "Bold", Source Han Serif SC;
        font-weight: bold;
        font-size: 36rpx;
        color: #111111;
        margin-bottom: 16rpx;
      }

      .case-desc {
        font-family: "Regular", Source Han Serif SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
      }

      .case-meta {
        margin-top: 14rpx;

        .meta-item {
          display: flex;
          align-items: center;
          margin-top: 8rpx;

          image {
            width: 40rpx;
            height: 40rpx;
            margin-top: 3px;
          }

          &:last-child {
            margin-top: 0;
          }

          .meta-text {
            margin-left: 12rpx;
            font-family: "Regular", Source Han Serif SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #565656;
          }
        }
      }
    }

    .custom-tabs-container {
      margin-top: 20rpx;
      overflow-x: auto;
      white-space: nowrap;

      &::-webkit-scrollbar {
        display: none;
        width: 0 !important;
        height: 0 !important;
        -webkit-appearance: none;
        background: transparent;
      }
    }

    .custom-tab {
      display: inline-block;
      vertical-align: top;
      margin-right: 45rpx;
      position: relative;
      padding-bottom: 10rpx;
      font-family: "Medium", Source Han Serif SC;
      font-weight: 500;
      font-size: 32rpx;
      color: #7b7b7b;
      cursor: pointer;
      transition: color 0.3s;
      white-space: nowrap;
      -webkit-tap-highlight-color: transparent;

      &.active {
        color: #000000;

        &::after {
          content: "";
          position: absolute;
          left: 50%;
          bottom: 0;
          transform: translateX(-50%);
          width: 52rpx;
          height: 6rpx;
          background: #9e2721;
          border-radius: 3rpx;
        }
      }
    }

    .case-swiper {
      // margin-top: 24rpx;
      transition: height 0.3s ease-in-out;
    }

    .solution-swiper-container {
      position: relative;
      margin-top: 24rpx;
    }

    .solution-swiper {
      height: 316rpx;
      // border-radius: 16rpx;
      overflow: hidden;
    }

    .solution-indicator-container {
      position: absolute;
      bottom: 0rpx;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      z-index: 5;

      .custom-dot {
        width: 12rpx;
        height: 12rpx;
        border-radius: 50%;
        background-color: #b8b8b8;
        margin: 0 8rpx;
        transition: background-color 0.3s;

        &.active {
          background-color: #9e2721;
        }
      }
    }

    .solution-item {
      position: relative;
      width: 100%;
      height: 100%;

      .solution-image-container {
        width: 100%;
        display: flex;
        gap: 19rpx;

        .solution-image1 {
          width: 200rpx;
          height: 280rpx;
          border-radius: 8rpx;
        }

        .solution-image2 {
          width: 466rpx;
          height: 280rpx;
          border-radius: 8rpx;
        }
      }

      .solution-title {
        position: absolute;
        bottom: 20rpx;
        left: 20rpx;
        color: #fff;
        font-size: 32rpx;
        font-weight: bold;
      }
    }

    .guarantee-grid {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 20rpx;
      margin-top: 24rpx;
    }

    .guarantee-item {
      // background-color: #f5f5f5;
      // border-radius: 8rpx;
      //padding: 30rpx 20rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .guarantee-image {
      border-radius: 8rpx;
      width: 216rpx;
      height: 316rpx;
      background: #d9d9d9;
    }

    .guarantee-title {
      margin-top: 24rpx;
      font-family: "Medium", Source Han Serif SC;
      font-weight: 500;
      font-size: 24rpx;
      color: #000000;
    }

    .dynamic-list {
      margin-top: 30rpx;
    }

    .dynamic-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx 0;
      border-bottom: 1rpx solid #eee;

      &:last-child {
        border-bottom: none;
      }
    }

    .dynamic-info {
      flex: 1;
      margin-right: 30rpx;
    }

    .dynamic-title {
      font-size: 30rpx;
      font-weight: 500;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      overflow: hidden;
      line-height: 1.6;
    }

    .dynamic-date {
      font-size: 24rpx;
      color: #999;
      margin-top: 10rpx;
    }

    .dynamic-image {
      width: 240rpx;
      height: 180rpx;
      border-radius: 12rpx;
      flex-shrink: 0;
    }

    .partners-section {
      padding-bottom: 40rpx;
    }

    .sphere-container {
      width: 100%;
      height: 350rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      perspective: 1000px;
      margin-top: 24rpx;
    }

    .sphere {
      width: 200rpx;
      height: 200rpx;
      position: relative;
      transform-style: preserve-3d;
      animation: rotate-sphere 25s linear infinite;
    }

    .sphere-item-placer {
      position: absolute;
      top: 50%;
      left: 50%;
      transform-style: preserve-3d;
    }

    .sphere-item {
      width: 75rpx;
      height: 75rpx;
      margin-left: -37.5rpx;
      margin-top: -37.5rpx;
      border-radius: 50%;
      overflow: hidden;
      background: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
      box-shadow: 0 0 12px rgba(0, 0, 0, 0.15);
      animation: rotate-item-reverse 25s linear infinite;
    }

    .sphere-item image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    @keyframes rotate-sphere {
      from {
        transform: rotateY(0deg);
      }

      to {
        transform: rotateY(360deg);
      }
    }

    @keyframes rotate-item-reverse {
      from {
        transform: rotateY(0deg);
      }

      to {
        transform: rotateY(-360deg);
      }
    }

    .partners-image {
      width: 100%;
    }

    .vision-video {
      position: relative;
      margin-top: 30rpx;
    }

    .vision-video-player {
      width: 100%;
      height: 386rpx;
      border-radius: 8rpx;
    }

    .vision-desc {
      font-family: 'Regular', Source Han Serif SC;
      margin-top: 24rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      line-height: 1.6;
      margin-bottom: 30rpx;
    }
  }
}
</style>
