<template>
  <view class="progress-container">
    <c-loading2></c-loading2>
    <section class="nav-bar">
      <c-navBar title="项目进度详情" isPerch :isBack="true" backIcon="/static/common/backBlack.png">
      </c-navBar>
    </section>
    <scroll-view class="page-scroll" scroll-y enhanced :bounces="false" :show-scrollbar="false"
      v-if="projectList.length > 0">
      <!-- 项目卡片列表 -->
      <view class="project-list">
        <view class="project-card" v-for="(project, index) in projectList" :key="index">
          <!-- 项目基本信息 -->
          <view class="project-header">
            <view class="project-title">{{ project.title }}</view>
            <view class="project-info">
              <view class="info-item">
                <text class="info-label">项目周期：</text>
                <text class="info-value">{{ project.period }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">项目阶段：</text>
                <text class="info-value">{{ project.stage }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">项目进度：</text>
                <view class="progress-container-new">
                  <view class="progress-bg">
                    <view class="progress-bar" :style="{ width: project.progressWidth }">
                      <view class="progress-tag" :class="project.progressClass">{{ project.progress }}</view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 进度信息 - 可展开内容 -->
            <view class="progress-content" v-if="project.expanded">
              <view class="progress-title">进度信息</view>
              <view class="timeline">
                <view class="timeline-item" v-for="(step, stepIndex) in project.steps" :key="stepIndex"
                  :class="step.completed ? 'completed' : (step.current ? 'current' : '')">
                  <view class="timeline-dot"></view>
                  <view class="timeline-content">
                    <view class="step-header">
                      <text class="step-title">{{ step.title }}</text>
                      <text class="step-time">{{ step.time }}</text>
                    </view>
                    <view class="step-desc">{{ step.description }}</view>
                    <!-- 步骤图片 -->
                    <view class="step-images" v-if="step.images && step.images.length > 0">
                      <image v-for="(img, imgIndex) in step.images" :key="imgIndex" :src="img" class="step-image"
                        mode="aspectFill" @click="previewImage(step.images, imgIndex)" />
                    </view>
                  </view>
                </view>
              </view>

              <!-- 收起按钮 -->
              <view class="collapse-btn" @click="toggleExpand(index)">
                <text class="collapse-text">收起</text>
                <u-icon name="arrow-up" color="#9E2721" size="24rpx" bold></u-icon>
              </view>
            </view>
            <!-- 展开/收起按钮 -->
            <view class="expand-btn" @click="toggleExpand(index)" v-if="!project.expanded">
              <text class="expand-text">展开</text>
              <u-icon name="arrow-down" color="#9E2721" size="24rpx" bold></u-icon>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    <!-- 暂无数据 -->
    <view class="no-data" v-if="!projectList.length && !isLoading">
      <image src="/static/common/nodata.png" class="no-data-image" mode="aspectFit"></image>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusHeight: 0,
      navHeight: 44,
      projectList: [],
      isLoading: true
    };
  },
  onShow() {
    // 获取系统信息
    const systemInfo = uni.getSystemInfoSync();
    this.statusHeight = systemInfo.statusBarHeight || 0;
    // 获取导航栏高度
    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    this.navHeight = menuButtonInfo.height + (menuButtonInfo.top - this.statusHeight) * 2;
    this.navHeight = 44; // 固定高度
  },
  onLoad() {
    // 显示loading
    this.$u.vuex("vuex_loading2", true);

    this.getProjectList();

    // 延迟隐藏loading
    this.$nextTick(() => {
      setTimeout(() => {
        this.$u.vuex("vuex_loading2", false);
      }, 1000);
    });
  },
  methods: {
    // 获取项目列表
    async getProjectList() {
      this.isLoading = true;
      try {
        const res = await this.$api.projectList();
        console.log("项目列表:", res);
        this.projectList = this.processProjectData(res.data);
      } catch (error) {
        console.log("项目列表:", error);
      } finally {
        this.isLoading = false;
      }
    },
    // 处理项目数据
    processProjectData(data) {
      return data.map(project => {
        // 计算进度百分比
        const progressNum = parseInt(project.projectProgress) || 0;
        const progressPercent = progressNum + '%';

        // 设置进度样式类
        let progressClass = 'progress-red';
        if (progressNum >= 70) {
          progressClass = 'progress-green';
        } else if (progressNum >= 30) {
          progressClass = 'progress-orange';
        }

        // 处理进度详情
        const processedSteps = project.progressDetails ? project.progressDetails.map(step => {
          // 处理图片数组
          let imageArray = [];
          if (step.images) {
            try {
              // 解析 JSON 字符串格式的图片数组
              const parsedImages = JSON.parse(step.images);
              imageArray = parsedImages.map(img => {
                // 如果是完整URL则直接使用，否则拼接前缀
                return img.startsWith('http') ? img : this.vuex_imgUrl + img;
              });
            } catch (error) {
              console.log('图片解析错误:', error);
              imageArray = [];
            }
          }

          return {
            title: step.projectStage,
            time: this.$u.timeFormat(step.updateTime, 'yyyy.mm.dd'),
            description: step.description,
            completed: step.status === 1,
            current: step.delFlag === 0,
            images: imageArray
          };
        }) : [];

        return {
          id: project.id,
          title: project.projectName,
          period: `${this.$u.timeFormat(project.startDate, 'yyyy.mm.dd')}-${this.$u.timeFormat(project.endDate, 'yyyy.mm.dd')}`,
          stage: project.projectStage,
          progress: progressPercent,
          progressClass: progressClass,
          progressWidth: progressPercent,
          expanded: false,
          steps: processedSteps
        };
      });
    },
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },
    // 显示更多操作
    showMore() {
      uni.showActionSheet({
        itemList: ['收藏', '举报'],
        success: (res) => {
          console.log('选中了第' + (res.tapIndex + 1) + '个按钮');
        }
      });
    },
    // 显示帮助
    showHelp() {
      uni.showToast({
        title: '帮助功能',
        icon: 'none'
      });
    },
    // 图片预览
    previewImage(urls, currentIndex) {
      console.log('预览图片:', urls, currentIndex);
      uni.previewImage({
        current: currentIndex,
        urls: urls,
        success: (res) => {
          console.log('图片预览成功:', res);
        },
        fail: (err) => {
          console.log('图片预览失败:', err);
          uni.showToast({
            title: '图片预览失败',
            icon: 'none'
          });
        }
      });
    },
    // 显示帮助
    showHelp() {
      uni.showToast({
        title: '帮助功能',
        icon: 'none'
      });
    },
    // 切换展开/收起状态
    toggleExpand(index) {
      this.projectList[index].expanded = !this.projectList[index].expanded;
    },

  }
};
</script>

<style lang="scss" scoped>
.progress-container {
  width: 100%;
  height: 100vh;
  // background-color: #F7F7F7;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .nav-bar {
    font-family: 'Bold', Source Han Serif SC;
    font-weight: bold;
    font-size: 36rpx;
    color: #000000;
  }

  .page-scroll {
    height: calc(100vh - 168rpx);
    flex: 1;
    min-height: 0;
  }
}

.no-data {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .no-data-image {
    width: 156rpx;
    height: 192rpx;
  }
}

.project-list {
  padding: 24rpx 32rpx;

  .project-card {
    background: #FFFFFF;
    border-radius: 8rpx;
    margin-bottom: 24rpx;

    .project-header {
      padding: 20rpx;

      .project-title {
        font-family: 'Bold', Source Han Serif SC;
        font-weight: bold;
        font-size: 28rpx;
        color: #000000;
        margin-bottom: 24rpx;
      }
    }
  }

  .project-info {
    margin-bottom: 24rpx;

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-family: 'Medium', Source Han Serif SC;
        font-weight: 500;
        font-size: 24rpx;
        color: #666666;
      }

      .info-value {
        font-family: 'Medium', Source Han Serif SC;
        font-weight: 500;
        font-size: 24rpx;
        color: #000;
        flex: 1;
      }

      .progress-container-new {
        width: 148rpx;

        .progress-bg {
          height: 32rpx;
          background: #E0E3EA;
          border-radius: 16rpx;
          position: relative;
          display: flex;
          align-items: center;
          overflow: hidden;

          .progress-bar {
            height: 100%;
            border-radius: 16rpx;
            position: relative;
            transition: width 0.8s ease-in-out;
            min-width: 60rpx;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding-right: 8rpx;
          }
        }
      }
    }


  }
}

.progress-content {
  border-top: 2rpx solid #E3E4E5;

  .progress-title {
    font-family: 'Bold', Source Han Serif SC;
    font-weight: bold;
    font-size: 28rpx;
    color: #000000;
    text-transform: none;
    margin-bottom: 24rpx;
    padding-top: 24rpx;
  }

  .timeline {
    position: relative;

    .timeline-item {
      position: relative;
      padding-left: 60rpx;
      margin-bottom: 60rpx;

      &:last-child {
        margin-bottom: 30rpx;
      }

      &::before {
        content: '';
        position: absolute;
        left: 20rpx;
        top: 40rpx;
        bottom: -60rpx;
        width: 2rpx;
        background-color: #9E2721;
        border-left: 2rpx dashed #9E2721;
        background: none;
      }

      &:last-child::before {
        display: none;
      }

      .timeline-dot {
        position: absolute;
        left: 0rpx;
        top: 0rpx;
        width: 22rpx;
        height: 22rpx;
        border-radius: 50%;
        background-color: #fff;
        border: 10rpx solid #9E2721;
      }

      &.completed .timeline-dot {
        background-color: #fff;
        border: 10rpx solid #9E2721;
      }

      .timeline-content {
        padding: 24rpx;
        padding-top: 6rpx;

        .step-header {
          display: flex;
          align-items: center;
          margin-bottom: 12rpx;

          .step-title {
            font-family: 'SemiBold', Source Han Serif SC;
            font-weight: 600;
            font-size: 24rpx;
            color: #000000;
          }

          .step-time {
            font-family: 'SemiBold', Source Han Serif SC;
            font-weight: 600;
            font-size: 24rpx;
            color: #000000;
            margin-left: 12rpx;
          }
        }

        .step-desc {
          font-family: 'Regular', Source Han Serif SC;
          font-weight: 400;
          font-size: 24rpx;
          color: #666666;
          line-height: 1.6;
          margin-bottom: 12rpx;
        }

        .step-images {
          display: flex;
          flex-wrap: wrap;
          gap: 12rpx;

          .step-image {
            width: 168rpx;
            height: 120rpx;
          }
        }
      }
    }
  }
}

.progress-tag {
  border-radius: 16rpx;
  height: 24rpx;
  font-family: 'Medium', Source Han Serif SC;
  font-weight: 500;
  font-size: 16rpx;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  white-space: nowrap;
  width: 100%;
  height: 100%;
}

.progress-red {
  background-color: #9E2721;
}

.progress-orange {
  background-color: #ff9800;
}

.progress-green {
  background-color: #4caf50;
}

.expand-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx 0;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 24rpx;

  .expand-text {
    font-family: 'Medium', Source Han Serif SC;
    font-weight: 500;
    font-size: 24rpx;
    color: #9E2721;
    margin-right: 12rpx;
  }
}

.collapse-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
  margin-top: 20rpx;

  .collapse-text {
    font-family: 'Medium', Source Han Serif SC;
    font-weight: 500;
    font-size: 24rpx;
    color: #9E2721;
    margin-right: 12rpx;
  }
}
</style>
