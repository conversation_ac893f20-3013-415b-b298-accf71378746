<template>
    <view class="cae-address">
        <c-loading></c-loading>
        <section class="nav-bar">
            <c-navBar :title="type == 'add' ? '新增地址' : '编辑地址'" :type="1" isPerch :isBack="true" backIcon="/static/common/backBlack.png"> </c-navBar>
        </section>
        <section class="form">
            <view class="form-item">
                <text>收件人姓名</text>
                <view class="">
                    <u--input inputAlign="right" placeholder="请输入内容" border="none" fontSize="24rpx"
                        v-model="formData.expressName"></u--input>
                </view>
            </view>
            <view class="form-item">
                <text>联系电话</text>
                <view>
                    <u--input inputAlign="right" placeholder="请输入内容" border="none" fontSize="24rpx"
                        v-model="formData.expressPhone"></u--input>
                </view>
            </view>
            <view class="form-item" @click="show = true">
                <text>所在地区</text>
                <view>
                    <!-- <u--input inputAlign="right" placeholder="请输入内容" border="none" fontSize="24rpx" disabled
                        disabledColor="transparent" suffixIcon="arrow-right"
                        suffixIconStyle="font-size: 24rpx;color: #888990" v-model="formData.expressArea"
                        readonly="true"></u--input> -->
                    <view class="flex align-center justify-end gap-12" style="width: 100%">
                        <text v-if="formData.expressArea">{{ formData.expressArea }}</text>
                        <text v-else style="color: #c0c4cc">请选择地区</text>
                        <!-- <view slot="right" style="margin-top: 2rpx">
                            <u-icon name="arrow-right" size="24rpx" color="#999"></u-icon>
                        </view> -->
                    </view>
                </view>
            </view>
            <view class="form-item">
                <text>详细地址</text>
                <view>
                    <u--input inputAlign="right" placeholder="请输入内容" border="none" fontSize="24rpx"
                        v-model="formData.detailedAddress"></u--input>
                </view>
            </view>
            <view class="form-item">
                <text>是否设置为默认地址</text>
                <u-switch activeColor="#EC3015" size="16" v-model="isDefault"
                    @change="(val) => (formData.status = val ? 0 : 1)"></u-switch>
            </view>
        </section>
        <view class="footer flex align-center">
            <view class="btn" @click="hanldeSave"> 保存 </view>
        </view>
        <!-- 地区选择 -->
        <c-selectDistrict :show="show" @cancel="show = false" @confirm="handleConfirm"></c-selectDistrict>
    </view>
</template>

<script>
export default {
    data() {
        return {
            pageTpye: "add", // 页面类型 add 新增 edit 编辑
            value: "",
            isDefault: false,
            formData: {
                expressName: "", // 收件人姓名
                expressPhone: "", // 联系电话
                detailedAddress: "", // 详细地址
                expressArea: "", // 所在地区
                status: 1, // 是否默认 0:默认 1:非默认
            },
            show: false,
            cityList: [],
        };
    },
    onLoad(options) {
        this.pageTpye = options.type;

        if (options.address) {
            this.formData = JSON.parse(decodeURIComponent(options.address));
        }
        if (this.formData.status == 0) {
            this.isDefault = true;
        } else {
            this.isDefault = false;
        }
    },
    methods: {
        // 地区选择
        handleConfirm(e) {
            this.formData.expressArea = e.value.map((item) => item.name).join("-");
            this.show = false;
        },

        // 保存
        async hanldeSave() {
            try {
                let form = this.formData;
                if (this.id) {
                    this.form.id = this.id;
                }

                // 表单校验
                if (!form.expressName) {
                    uni.showToast({ title: "请填写收件人姓名", icon: "none" });
                    return;
                }
                if (!form.expressPhone) {
                    uni.showToast({ title: "请填写联系电话", icon: "none" });
                    return;
                }
                // 简单手机号校验（可根据实际需求调整正则）
                if (!/^1[3-9]\d{9}$/.test(form.expressPhone)) {
                    uni.showToast({ title: "请输入正确的手机号", icon: "none" });
                    return;
                }
                if (!form.expressArea) {
                    uni.showToast({ title: "请选择所在地区", icon: "none" });
                    return;
                }
                if (!form.detailedAddress) {
                    uni.showToast({ title: "请填写详细地址", icon: "none" });
                    return;
                }

                const res = await this.$api.addAndEditAddress(form);
                if (res.code === 200) {
                    uni.showToast({ title: "保存成功", icon: "success" });
                    setTimeout(() => {
                        uni.navigateBack(1);
                    }, 1000);
                }
            } catch (err) {
                console.error();
                uni.showToast({ title: "网络出了一点小问题", icon: "none" });
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.cae-address {
    .nav-bar {
        font-family: "Bold", Source Han Serif SC;
        font-weight: bold;
        font-size: 36rpx;
        color: #000000;
        flex-shrink: 0;
    }
    .form {
        padding: 0 30rpx;

        .form-item {
            padding: 36rpx 0;
            border-bottom: 1rpx solid #edeff0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 24rpx;

            >text {
                font-weight: bold;
                color: #1a1a1a;
                font-size: 28rpx;
            }

            >view {
                width: 60%;
            }
        }
    }

    .footer {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 24rpx 0;
        padding-bottom: calc(env(safe-area-inset-bottom) + 24rpx);
        width: 100%;
        display: flex;
        justify-content: center;
        background-color: #fff;
        z-index: 100;

        .btn {
            width: 686rpx;
            height: 88rpx;
            background: #9E2721;
            border-radius: 70rpx;
            color: #ffffff;
            font-size: 32rpx;
            font-weight: bold;
            font-family: "Source Han Serif SC", "Source Han Serif", serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}
</style>
