<template>
    <view class="view">
        <c-loading></c-loading>
        <section class="nav-bar">
            <c-navBar title="关于我们" isPerch isBack backIcon="/static/common/backBlack.png"> </c-navBar>
        </section>
        <section class="content">
            <view class="mf-font-28 mf-weight-bold mf-title"> 成都木风未来有限公司 </view>
            <view class="mf-font-24" style="color: #333; margin-top: 24rpx">
                成都木风未来科技有限公司是一家国家高新技术企业专注于为政企，客户和互联网领域提供专业的外包服务，公司以技术服务为核心。致力于为客户提供高质量的数字化解决方案。
            </view>
            <view class="cover">
                <u-image src="/static/mine/aboutUs-cover.png" width="100%" height="340rpx" radius="32rpx"
                    mode="aspectFill"></u-image>
            </view>
            <view class="call-us">
                <view class="mf-font-28 mf-weight-bold mf-title">联系我们</view>
                <view class="mf-font-24" style="color: #333; margin-top: 12rpx">Tel:028-576-96541</view>
                <view class="mf-font-24" style="color: #333; margin-top: 12rpx">邮箱地址:<EMAIL></view>
                <view class="mf-font-24" style="color: #333; margin-top: 12rpx">公司地址:四川省成都市高新区ACC中航城市广场2111</view>
            </view>
            <c-map :latitude="latitude" :longitude="longitude" :scale="mapScale" :markers="markers" :hide-logo="true"
                mapStyle="width: 100%; height: 320rpx;margin-top:24rpx"></c-map>
        </section>
    </view>
</template>

<script>
export default {
    data() {
        return {
            latitude: 30.590643,
            longitude: 104.065214,
            mapScale: 12,
            markers: [
                {
                    id: 0,
                    title: "木风未来科技",
                    description: "成都市高新区茂业中心B座3305 联系电话:1534542164",
                    tag: "",
                    avatar: "/static/mine/logo.png",
                    latitude: 30.590643,
                    longitude: 104.065214,
                    width: 20,
                    height: 2,
                    iconPath: "/static/mine/point.png",
                    customCallout: {
                        anchorX: 0,
                        anchorY: 3,
                        display: "ALWAYS",
                    },
                },
            ],
        };
    },
    methods: {},
};
</script>

<style lang="scss" scoped>
.view {
    min-height: 100vh;
    font-family: 'Regular', Source Han Serif SC;

    .nav-bar {
        font-family: "Bold", Source Han Serif SC;
        font-weight: bold;
        font-size: 36rpx;
        color: #000000;
        flex-shrink: 0;
    }

    .mf-title {
        font-family: 'SemiBold', Source Han Serif SC;
    }

    .content {
        padding: 38rpx 32rpx;

        .cover {
            margin-top: 24rpx;
        }

        .call-us {
            margin-top: 40rpx;
        }
    }
}
</style>
