<script>
export default {
  onLaunch: function () {
    // 预加载字体，提高后续页面加载速度
    this.preloadFonts();
  },
  onShow: function () {},
  onHide: function () {},
  methods: {
    // 预加载字体文件
    preloadFonts() {
      const fonts = [
        {
          family: 'Bold',
          url: 'https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/siyuan_font/SourceHanSerifSC-Bold.otf'
        },
        {
          family: 'ExtraLight',
          url: 'https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/siyuan_font/SourceHanSerifSC-ExtraLight.otf'
        },
        {
          family: 'Light',
          url: 'https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/siyuan_font/SourceHanSerifSC-Light.otf'
        },
        {
          family: 'Medium',
          url: 'https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/siyuan_font/SourceHanSerifSC-Medium.otf'
        },
        {
          family: 'Regular',
          url: 'https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/siyuan_font/SourceHanSerifSC-Regular.otf'
        },
        {
          family: 'SemiBold',
          url: 'https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/siyuan_font/SourceHanSerifSC-SemiBold.otf'
        }
      ];

      // 使用 loadFontFace API 预加载字体（如果支持）
      fonts.forEach(font => {
        try {
          // #ifdef H5
          if (document && document.fonts && document.fonts.load) {
            document.fonts.load(`16px ${font.family}`, '测试文字').catch(() => {
              console.log(`字体 ${font.family} 预加载失败`);
            });
          }
          // #endif

          // 通用预加载方法：创建隐藏元素触发字体加载
          this.createFontPreloader(font.family);
        } catch (error) {
          console.log(`字体 ${font.family} 预加载出错:`, error);
        }
      });
    },

    // 创建隐藏元素预加载字体
    createFontPreloader(fontFamily) {
      // #ifdef H5
      try {
        const preloader = document.createElement('div');
        preloader.style.fontFamily = fontFamily;
        preloader.style.position = 'absolute';
        preloader.style.left = '-9999px';
        preloader.style.top = '-9999px';
        preloader.style.fontSize = '1px';
        preloader.style.visibility = 'hidden';
        preloader.textContent = '字体预加载';
        document.body.appendChild(preloader);

        // 1秒后移除预加载元素
        setTimeout(() => {
          if (preloader.parentNode) {
            preloader.parentNode.removeChild(preloader);
          }
        }, 1000);
      } catch (error) {
        console.log(`创建字体预加载器失败:`, error);
      }
      // #endif
    }
  }
};
</script>

<style lang="scss">
@import "uview-ui/index.scss";
/* 字体定义 - 优化加载性能 */
@font-face {
  font-family: "Bold";
  src: url("https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/siyuan_font/SourceHanSerifSC-Bold.otf")
    format("opentype");
  font-display: swap; /* 优化字体加载体验 */
  font-weight: bold;
}

@font-face {
  font-family: "ExtraLight";
  src: url("https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/siyuan_font/SourceHanSerifSC-ExtraLight.otf")
    format("opentype");
  font-display: swap;
  font-weight: 200;
}

@font-face {
  font-family: "Light";
  src: url("https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/siyuan_font/SourceHanSerifSC-Light.otf")
    format("opentype");
  font-display: swap;
  font-weight: 300;
}

@font-face {
  font-family: "Medium";
  src: url("https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/siyuan_font/SourceHanSerifSC-Medium.otf")
    format("opentype");
  font-display: swap;
  font-weight: 500;
}

@font-face {
  font-family: "Regular";
  src: url("https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/siyuan_font/SourceHanSerifSC-Regular.otf")
    format("opentype");
  font-display: swap;
  font-weight: 400;
}

@font-face {
  font-family: "SemiBold";
  src: url("https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/siyuan_font/SourceHanSerifSC-SemiBold.otf")
    format("opentype");
  font-display: swap;
  font-weight: 600;
}

/* 全局字体优化 */
* {
  /* 字体渲染优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* 文本渲染优化 */
  text-rendering: optimizeLegibility;
}
</style>
