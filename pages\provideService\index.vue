<template>
    <view class="consultation-container">
        <c-loading2></c-loading2>
        <!-- 导航栏 -->
        <section class="nav-bar">
            <c-navBar title="提供服务" isPerch :isBack="true" backIcon="/static/common/backBlack.png">
            </c-navBar>
        </section>

        <!-- 主要内容 -->
        <view class="main-content">
            <!-- Tab切换 -->
            <view class="tab-container">
                <view class="tab-item" :class="activeTab === 'service' ? 'active' : ''" @click="switchTab('service')">
                    木风软件
                </view>
                <view class="tab-item" :class="activeTab === 'consultation' ? 'active' : ''"
                    @click="switchTab('consultation')">
                    木风设计
                </view>
            </view>

            <scroll-view class="page-scroll" scroll-y enhanced :bounces="false" :show-scrollbar="false">
                <!-- 我的服务内容 -->
                <view v-if="activeTab === 'service'" class="service-content">
                    <image
                        src="https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>"
                        mode="widthFix" />
                </view>

                <!-- 我的咨询内容 -->
                <view v-if="activeTab === 'consultation'" class="consultation-content">
                    <image
                        src="https://mfwl-mini-program.obs.cn-southwest-2.myhuaweicloud.com/mini_program/<EMAIL>"
                        mode="widthFix" />
                </view>
            </scroll-view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            activeTab: 'service' // 默认选中木风软件
        };
    },
    onLoad(options) {
        // 显示loading

        // 根据传入的参数设置默认选中的tab
        if (options.i) {
            if (options.i == '1') {
                this.activeTab = 'service'; // 木风软件
            } else if (options.i == '2') {
                this.activeTab = 'consultation'; // 木风设计
            }
        }
        this.$u.vuex("vuex_loading2", true);
        // 延迟隐藏loading
        this.$nextTick(() => {
            setTimeout(() => {
                this.$u.vuex("vuex_loading2", false);
            }, 1000);
        });
    },
    methods: {
        // 切换tab
        switchTab(tab) {
            this.activeTab = tab;
        }
    }
};
</script>

<style lang="scss" scoped>
.consultation-container {
    width: 100%;
    min-height: 100vh;

    .nav-bar {
        font-family: 'Bold', Source Han Serif SC;
        font-weight: bold;
        font-size: 36rpx;
        color: #000000;
    }

    .main-content {
        background: #F7F7F7;

        .tab-container {
            display: flex;
            padding: 0 32rpx;
            margin: 24rpx 0;

            .tab-item {
                flex: 1;
                text-align: center;
                height: 72rpx;
                background: #fff;
                font-family: 'Medium', Source Han Serif SC;
                font-weight: 500;
                font-size: 32rpx;
                color: #999999;
                line-height: 72rpx;

                &.active {
                    font-family: 'Bold', Source Han Serif SC;
                    font-weight: bold;
                    font-size: 32rpx;
                    color: #FFFFFF;
                    background: #9E2721;
                }

                &:first-child {
                    // margin-right: 20rpx;
                    border-radius: 58rpx 0rpx 0rpx 58rpx;

                }

                &:last-child {
                    border-radius: 0rpx 58rpx 58rpx 0rpx;

                }
            }
        }

        .page-scroll {
            height: calc(100vh - 200rpx);
            box-sizing: border-box;
        }

        .service-content,
        .consultation-content {
            padding: 32rpx;
            padding-bottom: 150rpx;

            image {
                width: 100%;
                height: auto;
                display: block;
                min-height: 200rpx;
            }
        }
    }
}
</style>
